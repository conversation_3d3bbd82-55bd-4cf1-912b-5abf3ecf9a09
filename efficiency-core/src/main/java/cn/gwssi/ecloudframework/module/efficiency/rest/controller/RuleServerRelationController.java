package cn.gwssi.ecloudframework.module.efficiency.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.RuleServerRelationManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 规则服务器关系控制器
 */
@RestController
@RequestMapping("/module/efficiency/relation")
public class RuleServerRelationController extends ControllerTools {
    
    @Resource
    private RuleServerRelationManager ruleServerRelationManager;

    /**
     * 获取规则服务器关系列表
     * @param pageQuery 分页查询参数
     * @param relation 关系查询条件
     * @return 关系列表
     */
    @PostMapping("/list")
    @ResponseBody
    public PageResult<RuleServerRelationDTO> list(PageQuery pageQuery, @RequestBody RuleServerRelationDTO relation) {
        return ruleServerRelationManager.list(pageQuery, relation);
    }

    /**
     * 获取关系详情
     * @param id 关系ID
     * @return 关系详情
     */
    @GetMapping("/get/{id}")
    public ResultMsg<RuleServerRelationDTO> get(@PathVariable("id") String id) {
        RuleServerRelation relation = ruleServerRelationManager.get(id);
        RuleServerRelationDTO relationDTO = BeanCopierUtils.transformBean(relation, RuleServerRelationDTO.class);
        return ResultMsg.SUCCESS(relationDTO);
    }

    /**
     * 保存关系数据
     * @param relationDTO 关系数据
     * @return 保存结果
     */
    @PostMapping("/save")
    @CatchErr
    public ResultMsg<String> save(@RequestBody RuleServerRelationDTO relationDTO) {
        RuleServerRelation relation = BeanCopierUtils.transformBean(relationDTO, RuleServerRelation.class);
        ruleServerRelationManager.create(relation);
        return ResultMsg.SUCCESS(relation.getId());
    }

    /**
     * 批量保存关系数据
     * @param relationList 关系数据列表
     * @return 保存结果
     */
    @PostMapping("/batch/save")
    @CatchErr
    public ResultMsg<String> batchSave(@RequestBody List<RuleServerRelationDTO> relationList) {
        List<RuleServerRelation> relations = BeanCopierUtils.transformList(relationList, RuleServerRelation.class);
        ruleServerRelationManager.saveBatch(relations);
        return ResultMsg.SUCCESS("批量保存成功，共 " + relations.size() + " 条记录");
    }

    /**
     * 根据规则ID获取关联的服务器列表
     * @param ruleId 规则ID
     * @return 服务器关系列表
     */
    @GetMapping("/rule/{ruleId}")
    @ResponseBody
    public ResultMsg<List<RuleServerRelation>> getByRuleId(@PathVariable("ruleId") String ruleId) {
        List<RuleServerRelation> relations = ruleServerRelationManager.getByRuleId(ruleId);
        return ResultMsg.SUCCESS(relations);
    }

    /**
     * 根据资源ID获取关联的规则列表
     * @param cInstId 资源ID
     * @return 规则关系列表
     */
    @GetMapping("/server/{cInstId}")
    @ResponseBody
    public ResultMsg<List<RuleServerRelation>> getByCInstId(@PathVariable("cInstId") String cInstId) {
        List<RuleServerRelation> relations = ruleServerRelationManager.getByCInstId(cInstId);
        return ResultMsg.SUCCESS(relations);
    }

    /**
     * 为规则批量关联服务器
     * @param ruleId 规则ID
     * @param serverIds 服务器ID列表
     * @return 关联结果
     */
    @PostMapping("/associate/{ruleId}")
    @CatchErr
    public ResultMsg<String> associateServersToRule(@PathVariable("ruleId") String ruleId, 
                                                    @RequestBody List<String> serverIds) {
        int count = ruleServerRelationManager.associateServersToRule(ruleId, serverIds);
        return ResultMsg.SUCCESS("成功关联 " + count + " 台服务器");
    }

    /**
     * 为规则取消关联服务器
     * @param ruleId 规则ID
     * @param serverIds 服务器ID列表
     * @return 取消关联结果
     */
    @PostMapping("/disassociate/{ruleId}")
    @CatchErr
    public ResultMsg<String> disassociateServersFromRule(@PathVariable("ruleId") String ruleId, 
                                                        @RequestBody List<String> serverIds) {
        int count = ruleServerRelationManager.disassociateServersFromRule(ruleId, serverIds);
        return ResultMsg.SUCCESS("成功取消关联 " + count + " 台服务器");
    }

    /**
     * 检查规则和服务器的关系是否存在
     * @param ruleId 规则ID
     * @param cInstId 资源ID
     * @return 是否存在
     */
    @GetMapping("/exists")
    public ResultMsg<Boolean> existsRelation(@RequestParam("ruleId") String ruleId, 
                                           @RequestParam("cInstId") String cInstId) {
        boolean exists = ruleServerRelationManager.existsRelation(ruleId, cInstId);
        return ResultMsg.SUCCESS(exists);
    }

    /**
     * 根据服务器群组获取服务器列表
     * @param serverGroup 服务器群组
     * @return 服务器关系列表
     */
    @GetMapping("/group/{serverGroup}")
    @ResponseBody
    public ResultMsg<List<RuleServerRelation>> getByServerGroup(@PathVariable("serverGroup") String serverGroup) {
        List<RuleServerRelation> relations = ruleServerRelationManager.getByServerGroup(serverGroup);
        return ResultMsg.SUCCESS(relations);
    }

    /**
     * 同步规则的服务器关系
     * @param ruleId 规则ID
     * @param serverGroups 服务器群组列表
     * @return 同步结果
     */
    @PostMapping("/sync/{ruleId}")
    @CatchErr
    public ResultMsg<String> syncRuleServerRelations(@PathVariable("ruleId") String ruleId, 
                                                     @RequestBody List<String> serverGroups) {
        boolean success = ruleServerRelationManager.syncRuleServerRelations(ruleId, serverGroups);
        if (success) {
            return ResultMsg.SUCCESS("同步成功");
        } else {
            return getWarnResult("同步失败");
        }
    }

    /**
     * 获取未关联任何规则的服务器列表
     * @return 未关联服务器列表
     */
    @GetMapping("/unassociated")
    @ResponseBody
    public ResultMsg<List<RuleServerRelation>> getUnassociatedServers() {
        List<RuleServerRelation> relations = ruleServerRelationManager.getUnassociatedServers();
        return ResultMsg.SUCCESS(relations);
    }

    /**
     * 删除关系数据
     * @param id 关系ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    @CatchErr
    public ResultMsg<String> delete(@PathVariable("id") String id) {
        ruleServerRelationManager.remove(id);
        return ResultMsg.SUCCESS("删除成功");
    }

    /**
     * 根据规则ID删除所有关系
     * @param ruleId 规则ID
     * @return 删除结果
     */
    @DeleteMapping("/rule/{ruleId}")
    @CatchErr
    public ResultMsg<String> deleteByRuleId(@PathVariable("ruleId") String ruleId) {
        int count = ruleServerRelationManager.deleteByRuleId(ruleId);
        return ResultMsg.SUCCESS("删除成功，共删除 " + count + " 条记录");
    }

    /**
     * 根据资源ID删除所有关系
     * @param cInstId 资源ID
     * @return 删除结果
     */
    @DeleteMapping("/server/{cInstId}")
    @CatchErr
    public ResultMsg<String> deleteByCInstId(@PathVariable("cInstId") String cInstId) {
        int count = ruleServerRelationManager.deleteByCInstId(cInstId);
        return ResultMsg.SUCCESS("删除成功，共删除 " + count + " 条记录");
    }
}
