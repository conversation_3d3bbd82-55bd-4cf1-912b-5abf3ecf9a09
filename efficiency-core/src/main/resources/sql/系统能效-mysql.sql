drop table if exists s_resource_efficiency;

drop table if exists s_resource_index;

drop table if exists s_resource_result;

drop table if exists s_rule_server_relation;

drop table if exists s_server_disk_index;

/*==============================================================*/
/* Table: s_resource_efficiency                                 */
/*==============================================================*/
create table s_resource_efficiency
(
    id                   varchar(64) not null comment '主键',
    rule_name            varchar(256) comment '规则名称',
    cpu_low_threshold    int comment 'CPU低阈值',
    cpu_high_threshold   int comment 'CPU高阈值',
    memory_low_threshold int comment '内存低阈值',
    memory_high_threshold int comment '内存高阈值',
    disk_low_threshold   int comment '磁盘低阈值',
    disk_high_threshold  int comment '磁盘高阈值',
    start_time           varchar(10) comment '有效业务段开始时间',
    end_time             varchar(10) comment '有效业务段结束时间',
    high_rate            int comment '任意高负荷阈值累计占比',
    low_rate             int comment '任意低负荷阈值累计占比',
    server_groups        varchar(3000) comment '服务器群组',
    create_user          varchar(50) comment '创建人名称',
    create_by            varchar(64) comment '创建人',
    create_time          timestamp comment '创建时间',
    update_user          varchar(50) comment '更新人名称',
    update_by            varchar(64) comment '更新人',
    update_time          timestamp comment '更新时间',
    primary key (id)
);

alter table s_resource_efficiency comment '资源效能规则';

/*==============================================================*/
/* Table: s_resource_index                                      */
/*==============================================================*/
create table s_resource_index
(
    id                   varchar(64) not null comment '主键',
    rule_id              varchar(64) comment '规则ID',
    server_group         varchar(256) comment '服务器群组',
    c_inst_id            varchar(64) comment '资源ID',
    ci_name              varchar(256) comment '资源名称',
    ip_address           varchar(256) comment '资源IP',
    cpu_rate             float(5,2),
   memory_rate          float(5,2),
   disk_rate            float(5,2),
   receive_time         timestamp,
   send_time            timestamp,
   same_day_status      varchar(10),
   application_name     varchar(256),
   collection_freq      int comment '采集频率',
   ext1                 text,
   primary key (id)
);

alter table s_resource_index comment '资源指标数据';

/*==============================================================*/
/* Table: s_resource_result                                     */
/*==============================================================*/
create table s_resource_result
(
    id                   varchar(64) not null comment '主键',
    relation_id          varchar(64) comment '关系表ID',
    rule_id              varchar(64) comment '规则ID',
    server_group         varchar(256) comment '服务器群组',
    c_inst_id            varchar(64) comment '资源ID',
    ci_name              varchar(256) comment '资源名称',
    ip_address           varchar(256) comment '资源IP',
    application_name     varchar(256) comment '关联应用名称',
    application_id       varchar(64) comment '关联应用ID',
    cpu_high_threshold   varchar(10) comment 'CPU是否是高负荷',
    cpu_low_threshold    varchar(10) comment 'CPU是否是低负荷',
    memory_high_threshold varchar(10) comment '内存是否是高负荷',
    memory_low_threshold varchar(10) comment '内存是否否是低负荷',
    disk_high_threshold  varchar(10) comment '磁盘是否是高负荷',
    disk_low_threshold   varchar(10) comment '磁盘是否是低负荷',
    cpu_days             int comment 'CPU高负荷累计天数',
    memory_days          int comment '内存高负荷累计天数',
    disk_days            int comment '磁盘高负荷累计天数',
    process_date         date comment '数据处理时间',
    primary key (id)
);

alter table s_resource_result comment '资源能效结果';

/*==============================================================*/
/* Table: s_rule_server_relation                                */
/*==============================================================*/
create table s_rule_server_relation
(
    id                   varchar(64) not null comment '主键',
    rule_id              varchar(64) comment '规则ID',
    server_group         varchar(256) comment '服务器群组',
    c_inst_id            varchar(64) comment '资源ID',
    ci_name              varchar(256) comment '资源名称',
    ip_address           varchar(256) comment '资源IP',
    resource_type        varchar(10) comment '资源类别:物理机、容器',
    create_user          varchar(50) comment '创建人名称',
    create_by            varchar(64) comment '创建人',
    create_time          timestamp comment '创建时间',
    update_user          varchar(50) comment '更新人名称',
    update_by            varchar(64) comment '更新人',
    update_time          timestamp comment '更新时间',
    primary key (id)
);

alter table s_rule_server_relation comment '规则服务器关系表';

/*==============================================================*/
/* Table: s_server_disk_index                                   */
/*==============================================================*/
create table s_server_disk_index
(
    id                   varchar(64) not null,
    resource_index_id    varchar(64),
    disk_name            varchar(256),
    disk_rate            float(5,2),
   server_group         varchar(256) comment '服务器群组',
   c_inst_id            varchar(64) comment '资源ID',
   ci_name              varchar(256) comment '资源名称',
   ip_address           varchar(256),
   receive_time         timestamp,
   primary key (id)
);

alter table s_server_disk_index comment '磁盘分区指标';


