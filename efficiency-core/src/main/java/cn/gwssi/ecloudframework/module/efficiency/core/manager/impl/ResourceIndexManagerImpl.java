package cn.gwssi.ecloudframework.module.efficiency.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceIndexDao;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 资源指标数据管理实现类
 */
@Service
public class ResourceIndexManagerImpl extends BaseManager<String, ResourceIndex>
        implements ResourceIndexManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceIndexManagerImpl.class);
    
    @Resource
    private ResourceIndexDao resourceIndexDao;

    @Override
    protected ResourceIndexDao getDao() {
        return resourceIndexDao;
    }

    @Override
    public PageResult<ResourceIndexDTO> list(PageQuery pageQuery, ResourceIndexDTO index) {
        PageHelperUtils.startPageAndOrderBy(pageQuery, "receive_time desc");
        List<ResourceIndexDTO> list = resourceIndexDao.list(index);
        return new PageResult<>(list);
    }

    @Override
    @Transactional
    public void saveBatch(List<ResourceIndex> indexes) {
        if (indexes != null && !indexes.isEmpty()) {
            try {
                resourceIndexDao.insertBatch(indexes);
            } catch (Exception e) {
                LOGGER.error("批量保存指标数据失败", e);
                throw new RuntimeException("批量保存指标数据失败：" + e.getMessage());
            }
        }
    }

    @Override
    public List<ResourceIndex> getByRuleIdAndTimeRange(String ruleId, Date startTime, Date endTime) {
        return resourceIndexDao.getByRuleIdAndTimeRange(ruleId, startTime, endTime);
    }

    @Override
    public List<ResourceIndex> getByCInstIdAndTimeRange(String cInstId, Date startTime, Date endTime) {
        return resourceIndexDao.getByCInstIdAndTimeRange(cInstId, startTime, endTime);
    }

    @Override
    public ResourceIndex getLatestByCInstId(String cInstId) {
        return resourceIndexDao.getLatestByCInstId(cInstId);
    }

    @Override
    @Transactional
    public int cleanHistoryData(Date beforeTime) {
        try {
            return resourceIndexDao.deleteBeforeTime(beforeTime);
        } catch (Exception e) {
            LOGGER.error("清理历史数据失败", e);
            throw new RuntimeException("清理历史数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getResourceUsageStatistics(String ruleId, Date startTime, Date endTime) {
        List<ResourceIndex> indexes = getByRuleIdAndTimeRange(ruleId, startTime, endTime);
        
        Map<String, Object> statistics = new HashMap<>();
        if (indexes.isEmpty()) {
            return statistics;
        }
        
        // 计算平均使用率
        double avgCpuRate = indexes.stream().mapToDouble(ResourceIndex::getCpuRate).average().orElse(0.0);
        double avgMemoryRate = indexes.stream().mapToDouble(ResourceIndex::getMemoryRate).average().orElse(0.0);
        double avgDiskRate = indexes.stream().mapToDouble(ResourceIndex::getDiskRate).average().orElse(0.0);
        
        // 计算最大使用率
        double maxCpuRate = indexes.stream().mapToDouble(ResourceIndex::getCpuRate).max().orElse(0.0);
        double maxMemoryRate = indexes.stream().mapToDouble(ResourceIndex::getMemoryRate).max().orElse(0.0);
        double maxDiskRate = indexes.stream().mapToDouble(ResourceIndex::getDiskRate).max().orElse(0.0);
        
        // 计算最小使用率
        double minCpuRate = indexes.stream().mapToDouble(ResourceIndex::getCpuRate).min().orElse(0.0);
        double minMemoryRate = indexes.stream().mapToDouble(ResourceIndex::getMemoryRate).min().orElse(0.0);
        double minDiskRate = indexes.stream().mapToDouble(ResourceIndex::getDiskRate).min().orElse(0.0);
        
        statistics.put("avgCpuRate", Math.round(avgCpuRate * 100.0) / 100.0);
        statistics.put("avgMemoryRate", Math.round(avgMemoryRate * 100.0) / 100.0);
        statistics.put("avgDiskRate", Math.round(avgDiskRate * 100.0) / 100.0);
        statistics.put("maxCpuRate", Math.round(maxCpuRate * 100.0) / 100.0);
        statistics.put("maxMemoryRate", Math.round(maxMemoryRate * 100.0) / 100.0);
        statistics.put("maxDiskRate", Math.round(maxDiskRate * 100.0) / 100.0);
        statistics.put("minCpuRate", Math.round(minCpuRate * 100.0) / 100.0);
        statistics.put("minMemoryRate", Math.round(minMemoryRate * 100.0) / 100.0);
        statistics.put("minDiskRate", Math.round(minDiskRate * 100.0) / 100.0);
        statistics.put("totalCount", indexes.size());
        
        return statistics;
    }

    @Override
    public List<ResourceIndex> getAbnormalResources(String ruleId, Date startTime, Date endTime) {
        List<ResourceIndex> indexes = getByRuleIdAndTimeRange(ruleId, startTime, endTime);
        List<ResourceIndex> abnormalResources = new ArrayList<>();
        
        for (ResourceIndex index : indexes) {
            if (EfficiencyConstants.SameDayStatus.ABNORMAL.getText().equals(index.getSameDayStatus())) {
                abnormalResources.add(index);
            }
        }
        
        return abnormalResources;
    }

    @Override
    public String calculateLoadStatus(ResourceIndex index, 
                                    Integer cpuHighThreshold, Integer cpuLowThreshold,
                                    Integer memoryHighThreshold, Integer memoryLowThreshold,
                                    Integer diskHighThreshold, Integer diskLowThreshold) {
        if (index == null) {
            return EfficiencyConstants.LoadStatus.NORMAL.getText();
        }
        
        Float cpuRate = index.getCpuRate();
        Float memoryRate = index.getMemoryRate();
        Float diskRate = index.getDiskRate();
        
        // 检查是否有高负荷
        if ((cpuRate != null && cpuRate >= cpuHighThreshold) ||
            (memoryRate != null && memoryRate >= memoryHighThreshold) ||
            (diskRate != null && diskRate >= diskHighThreshold)) {
            return EfficiencyConstants.LoadStatus.HIGH.getText();
        }
        
        // 检查是否有低负荷
        if ((cpuRate != null && cpuRate <= cpuLowThreshold) &&
            (memoryRate != null && memoryRate <= memoryLowThreshold) &&
            (diskRate != null && diskRate <= diskLowThreshold)) {
            return EfficiencyConstants.LoadStatus.LOW.getText();
        }
        
        return EfficiencyConstants.LoadStatus.NORMAL.getText();
    }
}
