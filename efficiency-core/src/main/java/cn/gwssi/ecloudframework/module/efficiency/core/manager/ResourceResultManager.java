package cn.gwssi.ecloudframework.module.efficiency.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源能效结果管理接口
 */
public interface ResourceResultManager extends Manager<String, ResourceResult> {
    
    /**
     * 获取资源能效结果列表
     * @param pageQuery 分页查询参数
     * @param result 结果查询条件
     * @return 分页结果
     */
    PageResult<ResourceResultDTO> list(PageQuery pageQuery, ResourceResultDTO result);

    /**
     * 批量保存结果数据
     * @param results 结果数据列表
     */
    void saveBatch(List<ResourceResult> results);

    /**
     * 根据规则ID和处理日期查询结果
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 结果列表
     */
    List<ResourceResult> getByRuleIdAndProcessDate(String ruleId, Date processDate);

    /**
     * 根据资源ID和时间范围查询结果
     * @param cInstId 资源ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果列表
     */
    List<ResourceResult> getByCInstIdAndDateRange(String cInstId, Date startDate, Date endDate);

    /**
     * 清理历史数据
     * @param beforeDate 日期点
     * @return 清理的记录数
     */
    int cleanHistoryData(Date beforeDate);

    /**
     * 获取能效统计报告
     * @param ruleId 规则ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计报告
     */
    Map<String, Object> getEfficiencyReport(String ruleId, Date startDate, Date endDate);

    /**
     * 根据规则ID和处理日期查询结果
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 结果列表
     */
    List<ResourceResult> getByRuleIdAndProcessDate(String ruleId, Date processDate);



    /**
     * 获取低负荷资源列表
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 低负荷资源列表
     */
    List<ResourceResult> getLowLoadResources(String ruleId, Date processDate);

    /**
     * 生成能效分析建议
     * @param result 能效结果
     * @return 分析建议
     */
    String generateRecommendation(ResourceResult result);

    /**
     * 计算综合负荷状态
     * @param result 能效结果
     * @return 综合负荷状态
     */
    String calculateOverallStatus(ResourceResult result);
}
