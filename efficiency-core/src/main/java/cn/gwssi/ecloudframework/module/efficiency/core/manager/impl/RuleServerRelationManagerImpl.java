package cn.gwssi.ecloudframework.module.efficiency.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.RuleServerRelationDao;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.RuleServerRelationManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则服务器关系管理实现类
 */
@Service
public class RuleServerRelationManagerImpl extends BaseManager<String, RuleServerRelation>
        implements RuleServerRelationManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(RuleServerRelationManagerImpl.class);
    
    @Resource
    private RuleServerRelationDao ruleServerRelationDao;

    @Override
    public PageResult<RuleServerRelationDTO> list(PageQuery pageQuery, RuleServerRelationDTO relation) {
        PageHelperUtils.startPageAndOrderBy(pageQuery, "create_time desc");
        List<RuleServerRelationDTO> list = ruleServerRelationDao.list(relation);
        return new PageResult<>(list);
    }

    @Override
    @Transactional
    public void saveBatch(List<RuleServerRelation> relations) {
        if (relations != null && !relations.isEmpty()) {
            try {
                ruleServerRelationDao.insertBatch(relations);
            } catch (Exception e) {
                LOGGER.error("批量保存关系数据失败", e);
                throw new RuntimeException("批量保存关系数据失败：" + e.getMessage());
            }
        }
    }

    @Override
    public List<RuleServerRelation> getByRuleId(String ruleId) {
        return ruleServerRelationDao.getByRuleId(ruleId);
    }

    @Override
    public List<RuleServerRelation> getByCInstId(String cInstId) {
        return ruleServerRelationDao.getByCInstId(cInstId);
    }

    @Override
    @Transactional
    public int associateServersToRule(String ruleId, List<String> serverIds) {
        if (StringUtils.isBlank(ruleId) || serverIds == null || serverIds.isEmpty()) {
            return 0;
        }
        
        try {
            List<RuleServerRelation> relations = new ArrayList<>();
            for (String serverId : serverIds) {
                // 检查关系是否已存在
                if (!existsRelation(ruleId, serverId)) {
                    RuleServerRelation relation = new RuleServerRelation();
                    relation.setRuleId(ruleId);
                    relation.setCInstId(serverId);
                    // 这里可以根据需要设置其他字段
                    relations.add(relation);
                }
            }
            
            if (!relations.isEmpty()) {
                saveBatch(relations);
            }
            
            return relations.size();
        } catch (Exception e) {
            LOGGER.error("关联服务器到规则失败", e);
            throw new RuntimeException("关联服务器到规则失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int disassociateServersFromRule(String ruleId, List<String> serverIds) {
        if (StringUtils.isBlank(ruleId) || serverIds == null || serverIds.isEmpty()) {
            return 0;
        }
        
        try {
            int count = 0;
            for (String serverId : serverIds) {
                List<RuleServerRelation> relations = ruleServerRelationDao.getByCInstId(serverId);
                for (RuleServerRelation relation : relations) {
                    if (ruleId.equals(relation.getRuleId())) {
                        remove(relation.getId());
                        count++;
                    }
                }
            }
            return count;
        } catch (Exception e) {
            LOGGER.error("取消关联服务器失败", e);
            throw new RuntimeException("取消关联服务器失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int deleteByRuleId(String ruleId) {
        try {
            return ruleServerRelationDao.deleteByRuleId(ruleId);
        } catch (Exception e) {
            LOGGER.error("根据规则ID删除关系失败", e);
            throw new RuntimeException("根据规则ID删除关系失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int deleteByCInstId(String cInstId) {
        try {
            return ruleServerRelationDao.deleteByCInstId(cInstId);
        } catch (Exception e) {
            LOGGER.error("根据资源ID删除关系失败", e);
            throw new RuntimeException("根据资源ID删除关系失败：" + e.getMessage());
        }
    }

    @Override
    public boolean existsRelation(String ruleId, String cInstId) {
        return ruleServerRelationDao.existsRelation(ruleId, cInstId);
    }

    @Override
    public List<RuleServerRelation> getByServerGroup(String serverGroup) {
        return ruleServerRelationDao.getByServerGroup(serverGroup);
    }

    @Override
    @Transactional
    public boolean syncRuleServerRelations(String ruleId, List<String> serverGroups) {
        if (StringUtils.isBlank(ruleId) || serverGroups == null) {
            return false;
        }
        
        try {
            // 删除现有关系
            deleteByRuleId(ruleId);
            
            // 根据服务器群组创建新关系
            List<RuleServerRelation> newRelations = new ArrayList<>();
            for (String serverGroup : serverGroups) {
                // 这里需要根据实际业务逻辑获取服务器群组下的服务器列表
                // 暂时使用示例逻辑
                List<RuleServerRelation> groupServers = getByServerGroup(serverGroup);
                for (RuleServerRelation server : groupServers) {
                    RuleServerRelation relation = new RuleServerRelation();
                    relation.setRuleId(ruleId);
                    relation.setServerGroup(serverGroup);
                    relation.setCInstId(server.getCInstId());
                    relation.setCiName(server.getCiName());
                    relation.setIpAddress(server.getIpAddress());
                    relation.setResourceType(server.getResourceType());
                    newRelations.add(relation);
                }
            }
            
            if (!newRelations.isEmpty()) {
                saveBatch(newRelations);
            }
            
            return true;
        } catch (Exception e) {
            LOGGER.error("同步规则服务器关系失败", e);
            return false;
        }
    }

    @Override
    public List<RuleServerRelation> getUnassociatedServers() {
        // 这里需要根据实际业务逻辑实现
        // 可能需要查询所有服务器，然后过滤掉已关联的
        // 暂时返回空列表
        return new ArrayList<>();
    }
}
