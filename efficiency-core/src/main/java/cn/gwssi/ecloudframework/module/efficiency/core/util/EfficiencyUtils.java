package cn.gwssi.ecloudframework.module.efficiency.core.util;

import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 系统能效工具类
 */
public class EfficiencyUtils {
    
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_TIME_FORMAT);
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_DATE_FORMAT);
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_DATETIME_FORMAT);

    /**
     * 解析业务时间
     * @param date 日期
     * @param timeStr 时间字符串（HH:mm格式）
     * @return 解析后的时间
     */
    public static Date parseBusinessTime(Date date, String timeStr) {
        if (date == null || StringUtils.isBlank(timeStr)) {
            return null;
        }
        
        try {
            String dateStr = DATE_FORMAT.format(date);
            String datetimeStr = dateStr + " " + timeStr + ":00";
            return DATETIME_FORMAT.parse(datetimeStr);
        } catch (ParseException e) {
            throw new RuntimeException("解析业务时间失败：" + timeStr, e);
        }
    }

    /**
     * 计算使用率百分比
     * @param value 使用值
     * @param total 总值
     * @return 使用率百分比
     */
    public static float calculateUsageRate(float value, float total) {
        if (total <= 0) {
            return 0.0f;
        }
        return Math.round((value / total) * EfficiencyConstants.PERCENTAGE_BASE * 100.0f) / 100.0f;
    }

    /**
     * 格式化浮点数
     * @param value 浮点数值
     * @param precision 精度
     * @return 格式化后的值
     */
    public static float formatFloat(float value, int precision) {
        double multiplier = Math.pow(10, precision);
        return (float) (Math.round(value * multiplier) / multiplier);
    }

    /**
     * 格式化浮点数（使用默认精度）
     * @param value 浮点数值
     * @return 格式化后的值
     */
    public static float formatFloat(float value) {
        return formatFloat(value, EfficiencyConstants.FLOAT_PRECISION);
    }

    /**
     * 判断时间是否在业务时间段内
     * @param checkTime 检查时间
     * @param startTime 开始时间字符串（HH:mm格式）
     * @param endTime 结束时间字符串（HH:mm格式）
     * @return 是否在业务时间段内
     */
    public static boolean isInBusinessTime(Date checkTime, String startTime, String endTime) {
        if (checkTime == null || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return false;
        }
        
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(checkTime);
            
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int checkMinutes = hour * 60 + minute;
            
            String[] startParts = startTime.split(":");
            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            
            String[] endParts = endTime.split(":");
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);
            
            // 处理跨天的情况
            if (endMinutes < startMinutes) {
                return checkMinutes >= startMinutes || checkMinutes <= endMinutes;
            } else {
                return checkMinutes >= startMinutes && checkMinutes <= endMinutes;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成唯一ID
     * @return 唯一ID
     */
    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 计算两个时间之间的分钟数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分钟数
     */
    public static long calculateMinutesBetween(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return (endTime.getTime() - startTime.getTime()) / (1000 * 60);
    }

    /**
     * 获取日期的开始时间（00:00:00）
     * @param date 日期
     * @return 开始时间
     */
    public static Date getStartOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取日期的结束时间（23:59:59）
     * @param date 日期
     * @return 结束时间
     */
    public static Date getEndOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 验证时间格式
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    public static boolean isValidTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return false;
        }
        try {
            TIME_FORMAT.parse(timeStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 验证阈值范围
     * @param lowThreshold 低阈值
     * @param highThreshold 高阈值
     * @return 是否有效
     */
    public static boolean isValidThresholdRange(Integer lowThreshold, Integer highThreshold) {
        if (lowThreshold == null || highThreshold == null) {
            return false;
        }
        return lowThreshold >= 0 && highThreshold <= 100 && lowThreshold < highThreshold;
    }

    /**
     * 计算负荷状态
     * @param rate 使用率
     * @param lowThreshold 低阈值
     * @param highThreshold 高阈值
     * @return 负荷状态
     */
    public static String calculateLoadStatus(Float rate, Integer lowThreshold, Integer highThreshold) {
        if (rate == null || lowThreshold == null || highThreshold == null) {
            return EfficiencyConstants.LoadStatus.NORMAL.getText();
        }
        
        if (rate >= highThreshold) {
            return EfficiencyConstants.LoadStatus.HIGH.getText();
        } else if (rate <= lowThreshold) {
            return EfficiencyConstants.LoadStatus.LOW.getText();
        } else {
            return EfficiencyConstants.LoadStatus.NORMAL.getText();
        }
    }

    /**
     * 解析JSON字符串为字符串列表
     * @param jsonStr JSON字符串
     * @return 字符串列表
     */
    public static List<String> parseJsonToStringList(String jsonStr) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(jsonStr)) {
            return result;
        }
        
        try {
            // 简单的JSON数组解析（假设格式为 ["item1","item2"]）
            jsonStr = jsonStr.trim();
            if (jsonStr.startsWith("[") && jsonStr.endsWith("]")) {
                jsonStr = jsonStr.substring(1, jsonStr.length() - 1);
                String[] items = jsonStr.split(",");
                for (String item : items) {
                    item = item.trim();
                    if (item.startsWith("\"") && item.endsWith("\"")) {
                        item = item.substring(1, item.length() - 1);
                    }
                    if (StringUtils.isNotBlank(item)) {
                        result.add(item);
                    }
                }
            }
        } catch (Exception e) {
            // 解析失败时返回空列表
        }
        
        return result;
    }

    /**
     * 将字符串列表转换为JSON字符串
     * @param stringList 字符串列表
     * @return JSON字符串
     */
    public static String convertStringListToJson(List<String> stringList) {
        if (stringList == null || stringList.isEmpty()) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < stringList.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("\"").append(stringList.get(i)).append("\"");
        }
        sb.append("]");
        
        return sb.toString();
    }

    /**
     * 获取当前时间的字符串表示
     * @return 当前时间字符串
     */
    public static String getCurrentTimeString() {
        return DATETIME_FORMAT.format(new Date());
    }

    /**
     * 获取昨天的日期
     * @return 昨天的日期
     */
    public static Date getYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 获取指定天数前的日期
     * @param days 天数
     * @return 指定天数前的日期
     */
    public static Date getDaysAgo(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        return calendar.getTime();
    }
}
