<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceEfficiencyDao">
    <resultMap id="ResourceEfficiency" type="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="cpuLowThreshold" column="cpu_low_threshold" jdbcType="INTEGER"/>
        <result property="cpuHighThreshold" column="cpu_high_threshold" jdbcType="INTEGER"/>
        <result property="memoryLowThreshold" column="memory_low_threshold" jdbcType="INTEGER"/>
        <result property="memoryHighThreshold" column="memory_high_threshold" jdbcType="INTEGER"/>
        <result property="diskLowThreshold" column="disk_low_threshold" jdbcType="INTEGER"/>
        <result property="diskHighThreshold" column="disk_high_threshold" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
        <result property="highRate" column="high_rate" jdbcType="INTEGER"/>
        <result property="lowRate" column="low_rate" jdbcType="INTEGER"/>
        <result property="serverGroups" column="server_groups" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_name, cpu_low_threshold, cpu_high_threshold, memory_low_threshold, memory_high_threshold,
        disk_low_threshold, disk_high_threshold, start_time, end_time, high_rate, low_rate, server_groups,
        create_user, create_by, create_time, update_user, update_by, update_time
    </sql>

    <select id="get" parameterType="java.lang.String" resultMap="ResourceEfficiency">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_efficiency
        WHERE id = #{id}
    </select>

    <select id="list" parameterType="cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO" 
            resultMap="ResourceEfficiency">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_efficiency
        <where>
            <if test="ruleName != null and ruleName != ''">
                AND rule_name LIKE CONCAT('%', #{ruleName}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                AND start_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND end_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getByRuleName" parameterType="java.lang.String" resultMap="ResourceEfficiency">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_efficiency
        WHERE rule_name = #{ruleName}
        LIMIT 1
    </select>

    <select id="getEnabledRules" resultMap="ResourceEfficiency">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_efficiency
        ORDER BY create_time DESC
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency">
        INSERT INTO s_resource_efficiency (
            id, rule_name, cpu_low_threshold, cpu_high_threshold, memory_low_threshold, memory_high_threshold,
            disk_low_threshold, disk_high_threshold, start_time, end_time, high_rate, low_rate, server_groups,
            create_user, create_by, create_time, update_user, update_by, update_time
        ) VALUES (
            #{id}, #{ruleName}, #{cpuLowThreshold}, #{cpuHighThreshold}, #{memoryLowThreshold}, #{memoryHighThreshold},
            #{diskLowThreshold}, #{diskHighThreshold}, #{startTime}, #{endTime}, #{highRate}, #{lowRate}, #{serverGroups},
            #{createUser}, #{createBy}, #{createTime}, #{updateUser}, #{updateBy}, #{updateTime}
        )
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency">
        UPDATE s_resource_efficiency SET
            rule_name = #{ruleName},
            cpu_low_threshold = #{cpuLowThreshold},
            cpu_high_threshold = #{cpuHighThreshold},
            memory_low_threshold = #{memoryLowThreshold},
            memory_high_threshold = #{memoryHighThreshold},
            disk_low_threshold = #{diskLowThreshold},
            disk_high_threshold = #{diskHighThreshold},
            start_time = #{startTime},
            end_time = #{endTime},
            high_rate = #{highRate},
            low_rate = #{lowRate},
            server_groups = #{serverGroups},
            update_user = #{updateUser},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM s_resource_efficiency WHERE id = #{id}
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO s_resource_efficiency (
            id, rule_name, cpu_low_threshold, cpu_high_threshold, memory_low_threshold, memory_high_threshold,
            disk_low_threshold, disk_high_threshold, start_time, end_time, high_rate, low_rate, server_groups,
            create_user, create_by, create_time, update_user, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.ruleName}, #{item.cpuLowThreshold}, #{item.cpuHighThreshold}, 
             #{item.memoryLowThreshold}, #{item.memoryHighThreshold}, #{item.diskLowThreshold}, #{item.diskHighThreshold},
             #{item.startTime}, #{item.endTime}, #{item.highRate}, #{item.lowRate}, #{item.serverGroups},
             #{item.createUser}, #{item.createBy}, #{item.createTime}, #{item.updateUser}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>


</mapper>
