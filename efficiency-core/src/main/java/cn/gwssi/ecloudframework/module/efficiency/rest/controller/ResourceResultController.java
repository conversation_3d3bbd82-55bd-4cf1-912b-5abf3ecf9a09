package cn.gwssi.ecloudframework.module.efficiency.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源能效结果控制器
 */
@RestController
@RequestMapping("/module/efficiency/result")
public class ResourceResultController {
    
    @Resource
    private ResourceResultManager resourceResultManager;

    /**
     * 获取资源能效结果列表
     * @param pageQuery 分页查询参数
     * @param result 结果查询条件
     * @return 结果列表
     */
    @PostMapping("/list")
    public PageResult<ResourceResultDTO> list(PageQuery pageQuery, @RequestBody ResourceResultDTO result) {
        return resourceResultManager.list(pageQuery, result);
    }

//    /**
//     * 获取结果详情
//     * @param id 结果ID
//     * @return 结果详情
//     */
//    @GetMapping("/get/{id}")
//    public ResultMsg<ResourceResultDTO> get(@PathVariable("id") String id) {
//        ResourceResult result = resourceResultManager.get(id);
//        ResourceResultDTO resultDTO = BeanCopierUtils.transformBean(result, ResourceResultDTO.class);
//
//        // 计算综合状态和建议
//        resultDTO.setOverallStatus(resourceResultManager.calculateOverallStatus(result));
//        resultDTO.setRecommendation(resourceResultManager.generateRecommendation(result));
//
//        return ResultMsg.SUCCESS(resultDTO);
//    }
//
//    /**
//     * 保存结果数据
//     * @param resultDTO 结果数据
//     * @return 保存结果
//     */
//    @PostMapping("/save")
//    @CatchErr
//    public ResultMsg<String> save(@RequestBody ResourceResultDTO resultDTO) {
//        ResourceResult result = BeanCopierUtils.transformBean(resultDTO, ResourceResult.class);
//        resourceResultManager.create(result);
//        return ResultMsg.SUCCESS(result.getId());
//    }
//
//    /**
//     * 批量保存结果数据
//     * @param resultList 结果数据列表
//     * @return 保存结果
//     */
//    @PostMapping("/batch/save")
//    @CatchErr
//    public ResultMsg<String> batchSave(@RequestBody List<ResourceResultDTO> resultList) {
//        List<ResourceResult> results = BeanCopierUtils.transformList(resultList, ResourceResult.class);
//        resourceResultManager.saveBatch(results);
//        return ResultMsg.SUCCESS("批量保存成功，共 " + results.size() + " 条记录");
//    }

    /**
     * 根据规则ID和处理日期查询结果
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 结果列表
     */
    @GetMapping("/rule/{ruleId}")
    public ResultMsg<List<ResourceResult>> getByRuleIdAndProcessDate(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("processDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date processDate) {
        List<ResourceResult> results = resourceResultManager.getByRuleIdAndProcessDate(ruleId, processDate);
        return ResultMsg.SUCCESS(results);
    }

    /**
     * 根据资源ID和时间范围查询结果
     * @param cInstId 资源ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果列表
     */
    @GetMapping("/resource/{cInstId}")
    public ResultMsg<List<ResourceResult>> getByCInstIdAndDateRange(
            @PathVariable("cInstId") String cInstId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        List<ResourceResult> results = resourceResultManager.getByCInstIdAndDateRange(cInstId, startDate, endDate);
        return ResultMsg.SUCCESS(results);
    }

    /**
     * 获取能效统计报告
     * @param ruleId 规则ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计报告
     */
    @GetMapping("/report/{ruleId}")
    public ResultMsg<Map<String, Object>> getEfficiencyReport(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        Map<String, Object> report = resourceResultManager.getEfficiencyReport(ruleId, startDate, endDate);
        return ResultMsg.SUCCESS(report);
    }



    /**
     * 获取低负荷资源列表
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 低负荷资源列表
     */
    @GetMapping("/low-load/{ruleId}")
    public ResultMsg<List<ResourceResult>> getLowLoadResources(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("processDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date processDate) {
        List<ResourceResult> results = resourceResultManager.getLowLoadResources(ruleId, processDate);
        return ResultMsg.SUCCESS(results);
    }

    /**
     * 清理历史数据
     * @param beforeDate 日期点
     * @return 清理结果
     */
    @PostMapping("/clean")
    @CatchErr
    public ResultMsg<String> cleanHistoryData(
            @RequestParam("beforeDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beforeDate) {
        int count = resourceResultManager.cleanHistoryData(beforeDate);
        return ResultMsg.SUCCESS("清理完成，共删除 " + count + " 条记录");
    }

    /**
     * 删除结果数据
     * @param id 结果ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    @CatchErr
    public ResultMsg<String> delete(@PathVariable("id") String id) {
        resourceResultManager.remove(id);
        return ResultMsg.SUCCESS("删除成功");
    }
}
