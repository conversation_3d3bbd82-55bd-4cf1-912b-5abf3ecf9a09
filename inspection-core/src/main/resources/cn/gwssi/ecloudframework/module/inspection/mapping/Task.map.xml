<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskDao">
    <resultMap id="Task" type="cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="planId" column="plan_id" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="taskReceiver" column="task_receiver" jdbcType="VARCHAR"/>
        <result property="taskTime" column="task_time" jdbcType="TIMESTAMP"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="receiver" column="receiver" jdbcType="VARCHAR"/>
        <result property="receiverName" column="receiver_name" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled" jdbcType="INTEGER"/>
        <result property="tType" column="t_type" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="subjectJson" column="subject_json" jdbcType="VARCHAR"/>
        <result property="files" column="files" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="instStatus" column="INST_STATUS" jdbcType="VARCHAR"/>
        <result property="instOrderNo" column="INST_ORDER_NO" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="orgUserName" column="org_user_name" jdbcType="VARCHAR"/>
        <result property="planEndTime" column="plan_end_time" jdbcType="TIMESTAMP"/>
        <result property="isSendNotice" column="is_send_notice" jdbcType="VARCHAR"/>
        <association property="template" javaType="cn.gwssi.ecloudframework.module.inspection.api.model.TemplateDTO">
            <result property="name" column="name_temp" jdbcType="VARCHAR"/>
            <result property="displayItem" column="display_item" jdbcType="VARCHAR"/>
            <result property="category" column="category_tem" jdbcType="VARCHAR"/>
            <result property="showResult" column="show_result" jdbcType="VARCHAR"/>
            <result property="uploadFile" column="upload_file" jdbcType="VARCHAR"/>
        </association>
    </resultMap>
    
    <resultMap id="Inst" type="cn.gwssi.ecloudframework.module.inspection.api.model.TaskInstDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="instId" column="inst_id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="status" column="ti_status" jdbcType="VARCHAR"/>
    </resultMap>
    
    <sql id="task_time">
        trunc(task_time) &lt;= trunc(sysdate)
    </sql>
    <sql id="task_time" databaseId="mysql">
        date(task_time) &lt;= curdate()
    </sql>
    
    <sql id="task_time_equal">
        trunc(task_time) = trunc(sysdate)
    </sql>
    <sql id="task_time_equal" databaseId="mysql">
        date(task_time) = curdate()
    </sql>
    
    <sql id="task_time_to_char">
        to_char(task_time, 'yyyy-MM-dd')
    </sql>
    <sql id="task_time_to_char" databaseId="mysql">
        date_format(task_time,'%Y-%m-%d')
    </sql>
    
    <!--  统计使用的过滤条件  -->
    <sql id="common_count_condition">
        it.enabled = '1' and <include refid="task_time" />
        <if test="keyword != null and keyword != ''">
            and (it.name like concat('%', #{keyword}, '%') or it.receiver_name like concat('%', #{keyword}, '%') or it2.name like concat('%', #{keyword}, '%'))
        </if>
        <if test="result != null and result != ''">
            and it.result = #{result}
        </if>
        <if test="taskTimeStart != null and taskTimeStart != ''">
            AND it.task_time >= #{taskTimeStart}
        </if>
        <if test="taskTimeEnd != null and taskTimeEnd != ''">
            AND it.task_time &lt;= #{taskTimeEnd}
        </if>
    </sql>
    
    <!--  每日巡检使用的过滤条件，按角色拼接条件  -->
    <sql id="common_filter">
        and it.enabled = '1'
        <if test="!isAdmin">
            and (
                -- 巡检人是我
                it.RECEIVER = #{userId}
                -- 待接收，分配给我的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '1' and p.TASK_RECEIVER like concat('%', #{userId}, '%'))
                -- 待接收，分配给我的运维组织的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '2' and p.TASK_RECEIVER like concat('%', #{orgId}, '%'))
                -- 待接收，我创建的计划
                <if test="isLeader">
                    or (it.STATE = '1' and p.CREATE_BY = #{userId})
                </if>
            )
        </if>
    </sql>
    
    <select id="list" resultMap="Task">
        select it.id, it.CATEGORY, it.TEMPLATE_ID, it.NAME, it.TASK_TIME, it.RECEIVE_TIME, it.FINISH_TIME, it.RECEIVER, it.RECEIVER_NAME, it.RESULT, it.ENABLED, it.STATE, it2.name as name_temp
        from i_task it
        left join i_template it2 on it.template_id = it2.id
        left join i_plan p on it.PLAN_ID = p.id
        where <include refid="task_time" />
            and it.enabled = '1'
        -- 待处理
        <if test="task.tab != null and task.tab == 1">
            and (
                -- 巡检人是我，或者【状态完成、结论异常、有工单、工单未完成的】
                (it.RECEIVER = #{userId} and (it.STATE != '3' or (it.RESULT = '2'
                    and exists (select 1 from i_task_inst ta where ta.task_id = it.id and ta.status != 'end'))))
                -- 待接收，分配给我的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '1' and p.TASK_RECEIVER like concat('%', #{userId}, '%'))
                -- 待接收，分配给我的运维组织的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '2' and p.TASK_RECEIVER like concat('%', #{orgId}, '%'))
                -- 待接收，我创建的计划
                <if test="isLeader">
                    or (it.STATE = '1' and p.CREATE_BY = #{userId})
                </if>
            )
        </if>
        -- 已完成
        <if test="task.tab != null and task.tab == 2">
            and ((it.RECEIVER = #{userId} or p.CREATE_BY = #{userId}) and it.STATE = '3' and (it.RESULT = '1'
                or (not exists (select 1 from i_task_inst ta where ta.task_id = it.id and ta.status != 'end'))))
        </if>
        <if test="task.name != null and task.name != ''">
            AND it.name LIKE CONCAT('%', #{task.name}, '%')
        </if>
        <if test="task.nameTemp != null and task.nameTemp != ''">
            AND it2.name LIKE CONCAT('%', #{task.nameTemp}, '%')
        </if>
        <if test="task.state != null and task.state != ''">
            AND #{task.state} like concat('%', it.state, '%')
        </if>
        <if test="task.receiverName != null and task.receiverName != ''">
            AND it.RECEIVER_NAME LIKE CONCAT('%', #{task.receiverName}, '%')
        </if>
        <if test="task.taskTimeStart != null and task.taskTimeStart != ''">
            AND it.task_time >= #{task.taskTimeStart}
        </if>
        <if test="task.taskTimeEnd != null and task.taskTimeEnd != ''">
            AND it.task_time &lt;= #{task.taskTimeEnd}
        </if>
        <if test="task.finishTimeStart != null and task.finishTimeStart != ''">
            AND it.finish_time >= #{task.finishTimeStart}
        </if>
        <if test="task.finishTimeEnd != null and task.finishTimeEnd != ''">
            AND it.finish_time &lt;= #{task.finishTimeEnd}
        </if>
        <if test="task.planId != null and task.planId != ''">
            AND it.plan_id = #{task.planId}
        </if>
        <if test="task.categoryTemp != null and task.categoryTemp != ''">
            AND it2.category = #{task.categoryTemp}
        </if>
        <if test="task.keyword != null and task.keyword != ''">
            and (it.name like concat('%', #{task.keyword}, '%') or it.receiver_name like concat('%', #{task.keyword}, '%') or it2.name like concat('%', #{task.keyword}, '%'))
        </if>
        <if test="task.result != null and task.result != ''">
            and it.result = #{task.result}
        </if>
        <if test="task.statusInst != null and task.statusInst != ''">
            AND
            <if test="task.statusInst == 2">
                NOT
            </if>
            exists (select 1 from i_task_inst ta where ta.task_id = it.id and ta.status != 'end')
        </if>
    </select>
    
    <select id="listAll" resultMap="Task">
        select t.id, t.CATEGORY, t.TEMPLATE_ID, t.NAME, t.TASK_TIME, t.RECEIVE_TIME, t.FINISH_TIME, t.RECEIVER, t.RECEIVER_NAME, t.RESULT, t.ENABLED, t.STATE, it.name as name_temp
        from i_task t
        left join i_template it on t.template_id = it.id
        <where>
            <if test="planId != null and planId != ''">
                AND t.plan_id = #{planId}
            </if>
        </where>
    </select>
    
    <select id="listInstByTaskId" resultMap="Inst">
        select ti.id, ti.inst_id, ti.task_id, ti.order_no, sdd.name_ as ti_status
        from i_task_inst ti
        left join module_it_work_order miwo on ti.inst_id = miwo.INST_ID
        left join sys_data_dict sdd on sdd.dict_key_ = 'ITSM_FLOW_STATUS' and sdd.key_ = miwo.STATUS_
        where ti.task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </select>
    
    <select id="listToday" resultType="java.util.HashMap">
        select it.id, it.name, p.end_time as endTime, upi.TEL as tel, case it.state when '1' then it.create_user else it.receiver_name end as receiverName
        from i_task it
        left join i_plan p on it.plan_id = p.id
        left join uomp_person_info upi on upi.ORG_USER_ID = (case it.state when '1' then it.create_by else it.receiver end)
        where <include refid="task_time_equal" /> <include refid="common_filter" />
            <if test="state != null and state != ''">
                AND it.state = #{state}
            </if>
            AND it.state != '3'
        order by p.end_time asc
    </select>
    
    <select id="listInst" resultType="java.util.HashMap">
        select iti.inst_id as instId, miwo.ORDER_TITLE as title, miwo.LEVEL_ as level, bt.ASSIGNEE_NAMES_ as assignee, upi.TEL as tel, miwo.STATUS_ as status, iti.create_time as createTime
        from i_task_inst iti
        left join i_task it on iti.task_id = it.id
        left join i_plan p on it.plan_id = p.id
        left join bpm_task bt on bt.INST_ID_ = iti.INST_ID
        left join uomp_person_info upi on bt.ASSIGNEE_ID_ = upi.ORG_USER_ID
        left join module_it_work_order miwo on iti.inst_id = miwo.INST_ID
        where 1=1 <include refid="common_filter" />
        <if test="date != null and date != ''">
            AND <include refid="task_time_to_char" /> = #{date}
        </if>
        order by iti.status desc
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO">
        INSERT INTO i_task
        (id, name, plan_id, template_id, category, task_receiver, task_time, receive_time, finish_time, receiver, receiver_name, result, state, enabled, t_type, comment, subject_json, files, create_user, create_by, create_time, update_user, update_by, update_time,is_send_notice)
        VALUES
        (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{planId,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR}, #{taskReceiver,jdbcType=VARCHAR}, #{taskTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP},
        #{finishTime,jdbcType=TIMESTAMP}, #{receiver,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
        #{enabled,jdbcType=INTEGER}, #{tType,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{subjectJson,jdbcType=VARCHAR}, #{files,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isSendNotice,jdbcType=VARCHAR})
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO">
        UPDATE i_task
        <set>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="planId != null and planId != ''">
                plan_id = #{planId,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null and templateId != ''">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != ''">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="taskReceiver != null and taskReceiver != ''">
                task_receiver = #{taskReceiver,jdbcType=VARCHAR},
            </if>
            <if test="taskTime != null">
                task_time = #{taskTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiver != null and receiver != ''">
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null and receiverName != ''">
                receiver_name = #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="result != null and result != ''">
                result = #{result,jdbcType=VARCHAR},
            </if>
            <if test="state != null and state != ''">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=INTEGER},
            </if>
            <if test="tType != null and tType != ''">
                t_type = #{tType,jdbcType=VARCHAR},
            </if>
            <if test="comment != null and comment != ''">
                comment = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="subjectJson != null and subjectJson != ''">
                subject_json = #{subjectJson,jdbcType=VARCHAR},
            </if>
            <if test="files != null and files != ''">
                files = #{files,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isSendNotice != null and isSendNotice != ''">
                is_send_notice = #{isSendNotice,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="get" parameterType="java.lang.String" resultMap="Task">
        SELECT * FROM i_task WHERE id = #{id}
    </select>
    
    <select id="getWithTemplate" parameterType="java.lang.String" resultMap="Task">
        SELECT t.*, i.display_item, i.category as category_tem, i.show_result, i.upload_file FROM i_task t
        left join i_template i on t.template_id = i.id
        where t.id = #{taskId}
    </select>

    <update id="updateUnreceivedTasksByPlanId">
        UPDATE i_task set enabled = #{enabled}
        WHERE plan_id = #{planId} AND state = '1'
    </update>

    <update id="updateUnreceivedTasksByPlanIdAndTime">
        UPDATE i_task set enabled = #{enabled}
        WHERE plan_id = #{planId} AND state = '1' and task_time <![CDATA[ >= ]]> #{restartTime}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO i_task
        (id, name, plan_id, template_id, category, task_receiver, task_time, state, enabled, t_type, subject_json, files,
        create_user, create_by, create_time, update_user, update_by, update_time, is_send_notice)
        VALUES
        <foreach collection="tasks" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.planId,jdbcType=VARCHAR},
            #{item.templateId,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.taskReceiver,jdbcType=VARCHAR},
            #{item.taskTime,jdbcType=TIMESTAMP}, #{item.state,jdbcType=VARCHAR}, #{item.enabled,jdbcType=INTEGER},
            #{item.tType,jdbcType=VARCHAR}, #{item.subjectJson,jdbcType=VARCHAR}, #{item.files,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUser,jdbcType=VARCHAR}, #{item.updateBy,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isSendNotice,jdbcType=VARCHAR})
        </foreach>
    </insert>
    
    <update id="updateBatch">
        UPDATE i_task
        <set>
            <if test="task.receiver != null and task.receiver != ''">
                receiver = #{task.receiver,jdbcType=VARCHAR},
            </if>
            <if test="task.receiverName != null and task.receiverName != ''">
                receiver_name = #{task.receiverName},
            </if>
            <if test="task.enabled != null and task.enabled != ''">
                enabled = #{task.enabled,jdbcType=INTEGER},
            </if>
            <if test="task.updateUser != null and task.updateUser != ''">
                update_user = #{task.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="task.updateBy != null and task.updateBy != ''">
                update_by = #{task.updateBy,jdbcType=VARCHAR},
            </if>
            <if test="task.updateTime != null and task.updateTime != ''">
                update_time = #{task.updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>
    
    <select id="getInspectors" parameterType="java.lang.String" resultType="cn.gwssi.ecloudframework.org.api.model.dto.UserDTO">
        SELECT u.id_ as userId, u.fullname_ as fullname FROM org_user u
          left join org_relation r on r.type_ = 'groupUser' and u.id_ = r.user_id_ and r.group_id_ = #{orgId}
          left join org_relation r1 on r1.type_ = 'userRole' and u.id_ = r1.user_id_
          left join org_role r2 on r2.id_ = r1.group_id_
        where r2.alias_ = #{role}
    </select>

    <select id="count" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(*) from i_task
        WHERE plan_id = #{planId}
    </select>

    <select id="getPendingByTaskTime"  resultMap="Task">
        SELECT
            t.*,
            ugo.ORG_ID as org_id,
            ugo.FULLNAME_ as org_user_name,
            p.end_time as plan_end_time
        from i_plan p
        left join i_task t on p.id = t.plan_id
        left join user_group_org ugo on t.create_by = ugo.ID_
        WHERE p.state = '2' and t.state = '1' and t.enabled = '1'
          and t.is_send_notice = '0' and t.task_time <![CDATA[ <= ]]> #{expireTime}
    </select>

    <select id="getProcessingByTaskTime"  resultMap="Task">
        SELECT
            t.*,
            ugo.ORG_ID as org_id,
            ugo.FULLNAME_ as org_user_name,
            p.end_time as plan_end_time
        from i_plan p
        left join i_task t on p.id = t.plan_id
        left join user_group_org ugo on t.receiver = ugo.ID_
        WHERE p.state = '2' and t.state = '2' and t.enabled = '1'
          and t.is_send_notice != '2' and t.task_time <![CDATA[ <= ]]> #{expireTime}
    </select>

    <update id="setDisableByPlanId">
        update i_task set enabled = 2
        where plan_id =  #{planId} and state != '3'
    </update>

    <delete id="deleteByPlanIdAndTaskTime">
        DELETE FROM i_task WHERE plan_id = #{planId} and task_time <![CDATA[ > ]]> #{taskTime}
    </delete>
    
    <select id="countStateAll" resultType="java.util.HashMap">
        SELECT it.state, COUNT(1) as count
        FROM i_task it
        left join i_template it2 on it.template_id = it2.id
        where <include refid="common_count_condition" />
        GROUP BY it.state
    </select>
    
    <select id="countInstAll" resultType="java.util.HashMap">
        select iti.status, it2.category, count(1) as count
        from i_task_inst iti
        left join i_task it on iti.task_id = it.id
        left join i_template it2 on it.template_id = it2.id
        where <include refid="common_count_condition" />
        group by iti.status, it2.category
    </select>
    
    <select id="countIndicatorAndInst" resultType="java.util.HashMap">
        select tmp1.date, tmp1.count as count1, tmp2.count as count2 from (
            select date(it.task_time) as date, count(1) as count
            from i_form_item ifi
            left join i_task it on ifi.task_id = it.id
            left join i_template it2 on it.template_id = it2.id
            LEFT JOIN sys_data_dict d on d.DICT_KEY_ = 'UOMP_INSPECT_RESULT' and d.NAME_ like concat('%/', ifi.`result`)
            where d.ID_ is not null and <include refid="common_count_condition" />
            group by date(it.task_time)
        ) tmp1
        left join (
            select date(it.task_time) as date, count(1) as count
            from i_task_inst iti
            left join i_task it on iti.task_id = it.id
            left join i_template it2 on it.template_id = it2.id
            where iti.status = 'end' and <include refid="common_count_condition" />
            group by date(it.task_time)
        ) tmp2 on tmp1.date = tmp2.date
        order by tmp1.date asc
    </select>
    
    <select id="countState" resultType="java.util.HashMap">
        select it.state, count(1) as count
        from i_task it
        LEFT JOIN i_plan p on it.PLAN_ID = p.id
        where <include refid="task_time_equal" /> and it.state in ('1', '2') <include refid="common_filter" />
        group by it.state
    </select>
    
    <select id="countInstState" resultType="java.util.HashMap">
        select iti.status, miwo.LEVEL_ as level, count(1) as count
        from i_task_inst iti
        left join module_it_work_order miwo on iti.INST_ID = miwo.INST_ID
        left join i_task it on iti.task_id = it.id
        LEFT JOIN i_plan p on it.PLAN_ID = p.id
        where <include refid="task_time_equal" /> <include refid="common_filter" />
        group by iti.status, miwo.LEVEL_
    </select>
    
    <select id="getWorkOrder" resultType="java.util.HashMap">
        select miwo.ORDER_TITLE as title, miwo.ORDER_NO as orderNo, miwo.applicant, miwo.APPLICANT_TEL as tel, miwo.DESC_ as orderDesc
        from module_it_work_order miwo
        where miwo.INST_ID = #{instId}
    </select>
    
    <select id="getInstOpinion" resultType="java.util.HashMap">
        select bto.CREATE_TIME_ as createTime, bto.DUR_MS_ as durMs, upi.tel, bto.STATUS_ as status,
               case when bto.STATUS_ = 'agree' and mih.HANDLE_LOG is null then mih2.HANDLE_LOG else mih.HANDLE_LOG end as opinion,
               case when bto.APPROVER_NAME_ is null then regexp_substr(bto.ASSIGN_INFO_, '[^\\-]+', 1, 2) else bto.APPROVER_NAME_ end as approverName
        from bpm_task_opinion bto
        left join module_it_handling mih on bto.task_id_ = mih.TASK_ID
        left join module_it_handling mih2 on bto.inst_id_ = mih2.INST_ID and mih2.TASK_ID is null
        left join uomp_person_info upi on upi.ORG_USER_ID = (case when bto.APPROVER_ is null then regexp_substr(bto.ASSIGN_INFO_, '[^\\-]+', 1, 3) else bto.APPROVER_ end)
        where bto.INST_ID_ = #{instId} and bto.status_ != 'start'
        order by bto.CREATE_TIME_ desc, mih.CREATE_TIME desc
    </select>
    
</mapper>
