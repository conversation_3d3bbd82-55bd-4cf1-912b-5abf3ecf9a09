<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceResultDao">
    <resultMap id="ResourceResult" type="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="applicationName" column="application_name" jdbcType="VARCHAR"/>
        <result property="applicationId" column="application_id" jdbcType="VARCHAR"/>
        <result property="cpuHighThreshold" column="cpu_high_threshold" jdbcType="VARCHAR"/>
        <result property="cpuLowThreshold" column="cpu_low_threshold" jdbcType="VARCHAR"/>
        <result property="memoryHighThreshold" column="memory_high_threshold" jdbcType="VARCHAR"/>
        <result property="memoryLowThreshold" column="memory_low_threshold" jdbcType="VARCHAR"/>
        <result property="diskHighThreshold" column="disk_high_threshold" jdbcType="VARCHAR"/>
        <result property="diskLowThreshold" column="disk_low_threshold" jdbcType="VARCHAR"/>
        <result property="cpuDays" column="cpu_days" jdbcType="INTEGER"/>
        <result property="memoryDays" column="memory_days" jdbcType="INTEGER"/>
        <result property="diskDays" column="disk_days" jdbcType="INTEGER"/>
        <result property="processDate" column="process_date" jdbcType="DATE"/>
    </resultMap>

    <resultMap id="ResourceResultDTO" type="cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="applicationName" column="application_name" jdbcType="VARCHAR"/>
        <result property="applicationId" column="application_id" jdbcType="VARCHAR"/>
        <result property="cpuHighThreshold" column="cpu_high_threshold" jdbcType="VARCHAR"/>
        <result property="cpuLowThreshold" column="cpu_low_threshold" jdbcType="VARCHAR"/>
        <result property="memoryHighThreshold" column="memory_high_threshold" jdbcType="VARCHAR"/>
        <result property="memoryLowThreshold" column="memory_low_threshold" jdbcType="VARCHAR"/>
        <result property="diskHighThreshold" column="disk_high_threshold" jdbcType="VARCHAR"/>
        <result property="diskLowThreshold" column="disk_low_threshold" jdbcType="VARCHAR"/>
        <result property="cpuDays" column="cpu_days" jdbcType="INTEGER"/>
        <result property="memoryDays" column="memory_days" jdbcType="INTEGER"/>
        <result property="diskDays" column="disk_days" jdbcType="INTEGER"/>
        <result property="processDate" column="process_date" jdbcType="DATE"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, relation_id, rule_id, server_group, c_inst_id, ci_name, ip_address, application_name, application_id,
        cpu_high_threshold, cpu_low_threshold, memory_high_threshold, memory_low_threshold,
        disk_high_threshold, disk_low_threshold, cpu_days, memory_days, disk_days, process_date
    </sql>

    <select id="get" parameterType="java.lang.String" resultMap="ResourceResult">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_result
        WHERE id = #{id}
    </select>

    <select id="list" parameterType="cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO" 
            resultMap="ResourceResultDTO">
        SELECT rr.<include refid="Base_Column_List"/>, re.rule_name
        FROM s_resource_result rr
        LEFT JOIN s_resource_efficiency re ON rr.rule_id = re.id
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND rr.rule_id = #{ruleId}
            </if>
            <if test="cInstId != null and cInstId != ''">
                AND rr.c_inst_id = #{cInstId}
            </if>
            <if test="ciName != null and ciName != ''">
                AND rr.ci_name LIKE CONCAT('%', #{ciName}, '%')
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                AND rr.ip_address LIKE CONCAT('%', #{ipAddress}, '%')
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                AND rr.server_group = #{serverGroup}
            </if>
            <if test="applicationName != null and applicationName != ''">
                AND rr.application_name LIKE CONCAT('%', #{applicationName}, '%')
            </if>
            <if test="startTime != null">
                AND rr.process_date >= #{startTime}
            </if>
            <if test="endTime != null">
                AND rr.process_date &lt;= #{endTime}
            </if>
        </where>
        ORDER BY rr.process_date DESC
    </select>

    <select id="getByRuleIdAndProcessDate" resultMap="ResourceResult">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_result
        WHERE rule_id = #{ruleId} AND process_date = #{processDate}
        ORDER BY ci_name
    </select>

    <select id="getByCInstIdAndDateRange" resultMap="ResourceResult">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_result
        WHERE c_inst_id = #{cInstId}
        <if test="startDate != null">
            AND process_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND process_date &lt;= #{endDate}
        </if>
        ORDER BY process_date DESC
    </select>

    <select id="getByRuleIdAndProcessDate" resultMap="ResourceResult">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_result
        WHERE rule_id = #{ruleId} AND process_date = #{processDate}
        ORDER BY ci_name
    </select>



    <select id="getLowLoadResources" resultMap="ResourceResult">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_result
        WHERE rule_id = #{ruleId} AND process_date = #{processDate}
        AND cpu_low_threshold = '是' AND memory_low_threshold = '是' AND disk_low_threshold = '是'
        ORDER BY ci_name
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult">
        INSERT INTO s_resource_result (
            id, relation_id, rule_id, server_group, c_inst_id, ci_name, ip_address, application_name, application_id,
            cpu_high_threshold, cpu_low_threshold, memory_high_threshold, memory_low_threshold,
            disk_high_threshold, disk_low_threshold, cpu_days, memory_days, disk_days, process_date
        ) VALUES (
            #{id}, #{relationId}, #{ruleId}, #{serverGroup}, #{cInstId}, #{ciName}, #{ipAddress}, #{applicationName}, #{applicationId},
            #{cpuHighThreshold}, #{cpuLowThreshold}, #{memoryHighThreshold}, #{memoryLowThreshold},
            #{diskHighThreshold}, #{diskLowThreshold}, #{cpuDays}, #{memoryDays}, #{diskDays}, #{processDate}
        )
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult">
        UPDATE s_resource_result SET
            relation_id = #{relationId},
            rule_id = #{ruleId},
            server_group = #{serverGroup},
            c_inst_id = #{cInstId},
            ci_name = #{ciName},
            ip_address = #{ipAddress},
            application_name = #{applicationName},
            application_id = #{applicationId},
            cpu_high_threshold = #{cpuHighThreshold},
            cpu_low_threshold = #{cpuLowThreshold},
            memory_high_threshold = #{memoryHighThreshold},
            memory_low_threshold = #{memoryLowThreshold},
            disk_high_threshold = #{diskHighThreshold},
            disk_low_threshold = #{diskLowThreshold},
            cpu_days = #{cpuDays},
            memory_days = #{memoryDays},
            disk_days = #{diskDays},
            process_date = #{processDate}
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM s_resource_result WHERE id = #{id}
    </delete>

    <delete id="deleteBeforeDate">
        DELETE FROM s_resource_result WHERE process_date &lt; #{beforeDate}
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO s_resource_result (
            id, relation_id, rule_id, server_group, c_inst_id, ci_name, ip_address, application_name, application_id,
            cpu_high_threshold, cpu_low_threshold, memory_high_threshold, memory_low_threshold,
            disk_high_threshold, disk_low_threshold, cpu_days, memory_days, disk_days, process_date
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.relationId}, #{item.ruleId}, #{item.serverGroup}, #{item.cInstId}, #{item.ciName}, 
             #{item.ipAddress}, #{item.applicationName}, #{item.applicationId}, #{item.cpuHighThreshold}, #{item.cpuLowThreshold}, 
             #{item.memoryHighThreshold}, #{item.memoryLowThreshold}, #{item.diskHighThreshold}, #{item.diskLowThreshold}, 
             #{item.cpuDays}, #{item.memoryDays}, #{item.diskDays}, #{item.processDate})
        </foreach>
    </insert>


</mapper>
