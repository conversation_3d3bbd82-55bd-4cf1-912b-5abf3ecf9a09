# 规则服务器关系管理 API 文档

## 概述
规则服务器关系管理模块提供了对效能规则与服务器资源关联关系的管理功能，包括关系的创建、查询、修改、删除等操作。

## 基础路径
```
/module/efficiency/relation
```

## API 接口

### 1. 获取关系列表
**接口地址：** `POST /module/efficiency/relation/list`

**请求参数：**
- PageQuery: 分页参数
- RuleServerRelationDTO: 查询条件

**请求示例：**
```json
{
  "pageQuery": {
    "page": 1,
    "limit": 10
  },
  "relation": {
    "ruleId": "rule001",
    "ciName": "服务器01",
    "resourceType": "物理机"
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": "relation001",
        "ruleId": "rule001",
        "serverGroup": "group1",
        "cInstId": "server001",
        "ciName": "服务器01",
        "ipAddress": "*************",
        "resourceType": "物理机",
        "ruleName": "测试规则",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取关系详情
**接口地址：** `GET /module/efficiency/relation/get/{id}`

**路径参数：**
- id: 关系ID

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": "relation001",
    "ruleId": "rule001",
    "serverGroup": "group1",
    "cInstId": "server001",
    "ciName": "服务器01",
    "ipAddress": "*************",
    "resourceType": "物理机",
    "createTime": "2024-01-01 10:00:00"
  }
}
```

### 3. 保存关系数据
**接口地址：** `POST /module/efficiency/relation/save`

**请求参数：**
```json
{
  "ruleId": "rule001",
  "serverGroup": "group1",
  "cInstId": "server001",
  "ciName": "服务器01",
  "ipAddress": "*************",
  "resourceType": "物理机"
}
```

**响应结果：**
```json
{
  "success": true,
  "data": "relation002",
  "msg": "保存成功"
}
```

### 4. 批量保存关系数据
**接口地址：** `POST /module/efficiency/relation/batch/save`

**请求参数：**
```json
[
  {
    "ruleId": "rule001",
    "serverGroup": "group1",
    "cInstId": "server001",
    "ciName": "服务器01",
    "ipAddress": "*************",
    "resourceType": "物理机"
  },
  {
    "ruleId": "rule001",
    "serverGroup": "group1",
    "cInstId": "server002",
    "ciName": "服务器02",
    "ipAddress": "*************",
    "resourceType": "容器"
  }
]
```

**响应结果：**
```json
{
  "success": true,
  "data": "批量保存成功，共 2 条记录"
}
```

### 5. 根据规则ID获取关联服务器
**接口地址：** `GET /module/efficiency/relation/rule/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "relation001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "ipAddress": "*************",
      "resourceType": "物理机"
    }
  ]
}
```

### 6. 根据资源ID获取关联规则
**接口地址：** `GET /module/efficiency/relation/server/{cInstId}`

**路径参数：**
- cInstId: 资源ID

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "relation001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "ipAddress": "*************",
      "resourceType": "物理机"
    }
  ]
}
```

### 7. 为规则批量关联服务器
**接口地址：** `POST /module/efficiency/relation/associate/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
```json
["server001", "server002", "server003"]
```

**响应结果：**
```json
{
  "success": true,
  "data": "成功关联 3 台服务器"
}
```

### 8. 为规则取消关联服务器
**接口地址：** `POST /module/efficiency/relation/disassociate/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
```json
["server001", "server002"]
```

**响应结果：**
```json
{
  "success": true,
  "data": "成功取消关联 2 台服务器"
}
```

### 9. 检查关系是否存在
**接口地址：** `GET /module/efficiency/relation/exists`

**请求参数：**
- ruleId: 规则ID
- cInstId: 资源ID

**响应结果：**
```json
{
  "success": true,
  "data": true
}
```

### 10. 根据服务器群组获取服务器
**接口地址：** `GET /module/efficiency/relation/group/{serverGroup}`

**路径参数：**
- serverGroup: 服务器群组

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "relation001",
      "ruleId": "rule001",
      "serverGroup": "group1",
      "cInstId": "server001",
      "ciName": "服务器01",
      "ipAddress": "*************",
      "resourceType": "物理机"
    }
  ]
}
```

### 11. 同步规则的服务器关系
**接口地址：** `POST /module/efficiency/relation/sync/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
```json
["group1", "group2", "group3"]
```

**响应结果：**
```json
{
  "success": true,
  "data": "同步成功"
}
```

### 12. 获取未关联服务器列表
**接口地址：** `GET /module/efficiency/relation/unassociated`

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "relation002",
      "cInstId": "server005",
      "ciName": "服务器05",
      "ipAddress": "*************",
      "resourceType": "物理机"
    }
  ]
}
```

### 13. 删除关系数据
**接口地址：** `DELETE /module/efficiency/relation/delete/{id}`

**路径参数：**
- id: 关系ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功"
}
```

### 14. 根据规则ID删除所有关系
**接口地址：** `DELETE /module/efficiency/relation/rule/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功，共删除 5 条记录"
}
```

### 15. 根据资源ID删除所有关系
**接口地址：** `DELETE /module/efficiency/relation/server/{cInstId}`

**路径参数：**
- cInstId: 资源ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功，共删除 2 条记录"
}
```

## 数据模型

### RuleServerRelationDTO
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 关系ID |
| ruleId | String | 规则ID |
| serverGroup | String | 服务器群组 |
| cInstId | String | 资源ID |
| ciName | String | 资源名称 |
| ipAddress | String | 资源IP |
| resourceType | String | 资源类别（物理机、容器） |
| ruleName | String | 规则名称 |
| isAssociated | Boolean | 是否已关联 |
| lastDataTime | String | 最近指标数据时间 |
| resourceStatus | String | 资源状态 |

## 错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 数据不存在 |
| 500 | 服务器内部错误 |
