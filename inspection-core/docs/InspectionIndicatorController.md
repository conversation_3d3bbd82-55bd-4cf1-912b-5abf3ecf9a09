**接口名称:**

获取巡检指标列表

**接口描述:**

获取巡检指标列表

**请求路径:**

/module/inspection/indicator/list

**请求方式:**

POST

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/json|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|serialVersionUID|long|N||
|DEFAULT_PAGE_SIZE|int|N||
|DEFAULT_PAGE_NO|int|N||
|pageSize|Integer|N||
|pageNo|Integer|N||
|sort|String|N||
|orderBy|String|N||
|noPage|Boolean|N||
|isShowTotal|boolean|N||


- Body

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|N|主键|
|indicator|String|N|巡检指标|
|method|String|N|巡检方法|
|standard|String|N|参考标准|
|category|String|N|所属分类|
|resultType|String|N|结果种类，选项勾选\人工填写|
|state|Integer|N|启用状态，1：启用，0：停用|
|dicType|String|N|字典选择，是/否，正常/异常|
|createTime|Date|N||
|createUser|String|N||
|createBy|String|N||
|editable|String|N|是否可编辑，非数据库字段|
|deletable|String|N|是否可删除，非数据库字段|
|createStartTime|String|N||
|createEndTime|String|N||


**请求示例:**

```Form
serialVersionUID=null&DEFAULT_PAGE_SIZE=null&DEFAULT_PAGE_NO=null&pageSize=null&pageNo=null&sort=null&orderBy=null&noPage=null&isShowTotal=null
```

```JSON
{
    "id": "",
    "indicator": "",
    "method": "",
    "standard": "",
    "category": "",
    "resultType": "",
    "state": 0,
    "dicType": "",
    "createTime": null,
    "createUser": "",
    "createBy": "",
    "editable": "",
    "deletable": "",
    "createStartTime": "",
    "createEndTime": ""
}
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|pageSize|Integer|N|分页大小|
|page|Integer|N|当前页|
|total|Integer|N|总条数|
|rows|List|N|分页列表数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|


**返回示例:**

```JSON
{
    "pageSize": 0,
    "page": 0,
    "total": 0,
    "rows": [],
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

**接口名称:**

获取巡检指标详情

**接口描述:**

获取巡检指标详情

**请求路径:**

/module/inspection/indicator/get

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|Y|巡检指标ID|


- Body



**请求示例:**

```Form
id=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|data|InspectionIndicatorDTO|N|结果数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|

- InspectionIndicatorDTO data

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|N|主键|
|indicator|String|N|巡检指标|
|method|String|N|巡检方法|
|standard|String|N|参考标准|
|category|String|N|所属分类|
|resultType|String|N|结果种类，选项勾选\人工填写|
|state|Integer|N|启用状态，1：启用，0：停用|
|dicType|String|N|字典选择，是/否，正常/异常|
|createTime|Date|N||
|createUser|String|N||
|createBy|String|N||
|editable|String|N|是否可编辑，非数据库字段|
|deletable|String|N|是否可删除，非数据库字段|
|createStartTime|String|N||
|createEndTime|String|N||


**返回示例:**

```JSON
{
    "data": {
        "id": "",
        "indicator": "",
        "method": "",
        "standard": "",
        "category": "",
        "resultType": "",
        "state": 0,
        "dicType": "",
        "createTime": null,
        "createUser": "",
        "createBy": "",
        "editable": "",
        "deletable": "",
        "createStartTime": "",
        "createEndTime": ""
    },
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

**接口名称:**

保存巡检指标

**接口描述:**

保存巡检指标

**请求路径:**

/module/inspection/indicator/save

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/json|Y||


- Param



- Body

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|N|主键|
|indicator|String|N|巡检指标|
|method|String|N|巡检方法|
|standard|String|N|参考标准|
|category|String|N|所属分类|
|resultType|String|N|结果种类，选项勾选\人工填写|
|state|Integer|N|启用状态，1：启用，0：停用|
|dicType|String|N|字典选择，是/否，正常/异常|
|createTime|Date|N||
|createUser|String|N||
|createBy|String|N||
|editable|String|N|是否可编辑，非数据库字段|
|deletable|String|N|是否可删除，非数据库字段|
|createStartTime|String|N||
|createEndTime|String|N||


**请求示例:**

```JSON
{
    "id": "",
    "indicator": "",
    "method": "",
    "standard": "",
    "category": "",
    "resultType": "",
    "state": 0,
    "dicType": "",
    "createTime": null,
    "createUser": "",
    "createBy": "",
    "editable": "",
    "deletable": "",
    "createStartTime": "",
    "createEndTime": ""
}
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|data|String|N|结果数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|


**返回示例:**

```JSON
{
    "data": "",
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

**接口名称:**

删除巡检指标

**接口描述:**

删除巡检指标

**请求路径:**

/module/inspection/indicator/remove

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|Y|巡检指标ID|


- Body



**请求示例:**

```Form
id=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|data|String|N|结果数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|


**返回示例:**

```JSON
{
    "data": "",
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

**接口名称:**

更新巡检指标状态

**接口描述:**

更新巡检指标状态

**请求路径:**

/module/inspection/indicator/updateState

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|Y|巡检指标ID|
|state|Integer|Y|状态值（1：启用，0：停用）|


- Body



**请求示例:**

```Form
id=null&state=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|data|String|N|结果数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|


**返回示例:**

```JSON
{
    "data": "",
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

**接口名称:**

根据模板ID获取关联的指标列表

**接口描述:**

根据模板ID获取关联的指标列表

**请求路径:**

/module/inspection/indicator/getByTemplateId

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|templateId|String|Y|模板ID|


- Body



**请求示例:**

```Form
templateId=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|data|List<InspectionIndicatorDTO>|N|结果数据|
|isOk|Boolean|N|本次调用是否成功|
|msg|String|N|操作提示信息|
|cause|String|N|异常堆栈信息|
|code|String|N|状态码|

- List<InspectionIndicatorDTO> data

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|element|InspectionIndicatorDTO|Y||

- InspectionIndicatorDTO element

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|String|N|主键|
|indicator|String|N|巡检指标|
|method|String|N|巡检方法|
|standard|String|N|参考标准|
|category|String|N|所属分类|
|resultType|String|N|结果种类，选项勾选\人工填写|
|state|Integer|N|启用状态，1：启用，0：停用|
|dicType|String|N|字典选择，是/否，正常/异常|
|createTime|Date|N||
|createUser|String|N||
|createBy|String|N||
|editable|String|N|是否可编辑，非数据库字段|
|deletable|String|N|是否可删除，非数据库字段|
|createStartTime|String|N||
|createEndTime|String|N||


**返回示例:**

```JSON
{
    "data": [
        {
            "id": "",
            "indicator": "",
            "method": "",
            "standard": "",
            "category": "",
            "resultType": "",
            "state": 0,
            "dicType": "",
            "createTime": null,
            "createUser": "",
            "createBy": "",
            "editable": "",
            "deletable": "",
            "createStartTime": "",
            "createEndTime": ""
        }
    ],
    "isOk": false,
    "msg": "",
    "cause": "",
    "code": ""
}
```

