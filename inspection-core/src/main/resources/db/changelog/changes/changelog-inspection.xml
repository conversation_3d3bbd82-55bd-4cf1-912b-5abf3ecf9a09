<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    
    <changeSet id="sql-1" author="duqiang" failOnError="false">
        <createTable tableName="I_FORM_ITEM" remarks="巡检单明细表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TASK_ID" type="VARCHAR(64)" remarks="任务id">
            </column>
            <column name="INDICATOR_ID" type="VARCHAR(64)" remarks="指标id">
            </column>
            <column name="DISPLAY_ITEM" type="VARCHAR(64)" remarks="巡检内容展示项">
            </column>
            <column name="SUBJECT_NAME" type="VARCHAR(64)" remarks="巡检对象">
            </column>
            <column name="RESULT" type="VARCHAR(64)" remarks="巡检结果">
            </column>
            <column name="COMMENT" type="VARCHAR(1000)" remarks="备注">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-3" author="duqiang" failOnError="false">
        <createTable tableName="I_INDICATOR" remarks="巡检指标表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="INDICATOR" type="VARCHAR(500)" remarks="巡检指标">
            </column>
            <column name="METHOD" type="VARCHAR(500)" remarks="巡检方法">
            </column>
            <column name="STANDARD" type="VARCHAR(500)" remarks="参考标准">
            </column>
            <column name="CATEGORY" type="VARCHAR(64)" remarks="所属分类">
            </column>
            <column name="RESULT_TYPE" type="VARCHAR(64)" remarks="结果种类，选项勾选\人工填写">
            </column>
            <column name="STATE" type="INT" remarks="启用状态，1：启用，0：停用">
            </column>
            <column name="DIC_TYPE" type="VARCHAR(64)" remarks="字典选择，是/否，正常/异常">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-5" author="duqiang" failOnError="false">
        <createTable tableName="I_PLAN" remarks="巡检计划表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TEMPLATE_ID" type="VARCHAR(64)" remarks="关联模板id">
            </column>
            <column name="NAME" type="VARCHAR(255)" remarks="计划名称">
            </column>
            <column name="DESCRIPTION" type="${clob.type}" remarks="描述">
            </column>
            <column name="CATEGORY" type="VARCHAR(64)" remarks="巡检类型，日巡检\周巡检\月巡检">
            </column>
            <column name="START_TIME" type="TIMESTAMP" remarks="开始时间">
            </column>
            <column name="END_TIME" type="TIMESTAMP" remarks="结束时间">
            </column>
            <column name="HOLIDAY_SKIP" type="INT" remarks="是否节假日跳过，1：是，0：否">
            </column>
            <column name="TASK_RECEIVER" type="${clob.type}" remarks="任务接收人">
            </column>
            <column name="TASK_RECEVIER_OBJ" type="INT" remarks="任务接收对象 0：人员；1：组织">
            </column>
            <column name="PLAN_TIME" type="VARCHAR(500)" remarks="计划巡检时间">
            </column>
            <column name="STATE" type="VARCHAR(64)" remarks="巡检状态">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
            <column name="IS_SEND_NOTICE" type="VARCHAR(2)" remarks="是否已发送过提醒,0:未发送 1:已发送">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-7" author="duqiang" failOnError="false">
        <createTable tableName="I_SUBJECT" remarks="巡检对象表">
            <column name="id" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="SUBJECT_TYPE" type="VARCHAR(255)">
            </column>
            <column name="SUBJECT_NAME" type="VARCHAR(255)">
            </column>
            <column name="PLAN_ID" type="VARCHAR(64)">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-9" author="duqiang" failOnError="false">
        <createTable tableName="I_TASK" remarks="巡检任务表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="NAME" type="VARCHAR(255)" remarks="任务名称">
            </column>
            <column name="PLAN_ID" type="VARCHAR(64)" remarks="计划id">
            </column>
            <column name="TEMPLATE_ID" type="VARCHAR(64)" remarks="模板id">
            </column>
            <column name="CATEGORY" type="VARCHAR(64)" remarks="巡检类型">
            </column>
            <column name="TASK_RECEIVER" type="${clob.type}" remarks="任务接收的组织机构">
            </column>
            <column name="TASK_TIME" type="TIMESTAMP" remarks="计划巡检时间">
            </column>
            <column name="RECEIVE_TIME" type="TIMESTAMP" remarks="接收时间">
            </column>
            <column name="FINISH_TIME" type="TIMESTAMP" remarks="完成时间">
            </column>
            <column name="RECEIVER" type="VARCHAR(64)" remarks="巡检人">
            </column>
            <column name="RECEIVER_NAME" type="VARCHAR(64)" remarks="巡检人姓名">
            </column>
            <column name="RESULT" type="VARCHAR(64)" remarks="巡检结论，正常\异常">
            </column>
            <column name="STATE" type="VARCHAR(64)" remarks="完成状态，待接收\处理中\已完成">
            </column>
            <column name="ENABLED" type="INT" remarks="是否有效，1：是，0：否">
            </column>
            <column name="T_TYPE" type="VARCHAR(64)" remarks="任务类型，计划中\计划外">
            </column>
            <column name="COMMENT" type="VARCHAR(500)" remarks="备注">
            </column>
            <column name="SUBJECT_JSON" type="${clob.type}" remarks="巡检对象，json">
            </column>
            <column name="FILES" type="${clob.type}" remarks="附件，json">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
            <column name="IS_SEND_NOTICE" type="VARCHAR(2)" remarks="是否已发送过提醒,0:未发送 1:发送了未接收提醒 2:发送了录入提醒">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-11" author="duqiang" failOnError="false">
        <createTable tableName="I_TEMPLATE" remarks="模板表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CATEGORY" type="VARCHAR(64)" remarks="模板类型">
            </column>
            <column name="NAME" type="VARCHAR(512)" remarks="模板名称">
            </column>
            <column name="BASIC_INFO" type="VARCHAR(64)" remarks="基本信息">
            </column>
            <column name="DISPLAY_ITEM" type="VARCHAR(64)" remarks="展示项">
            </column>
            <column name="SHOW_RESULT" type="INT" remarks="是否展示结论，1：是，0：否">
            </column>
            <column name="DISPOSE" type="VARCHAR(64)" remarks="处置方式，手动转工单\自动转工单">
            </column>
            <column name="UPLOAD_FILE" type="INT" remarks="是否支持上传，1：是，0：否">
            </column>
            <column name="SCOPE" type="VARCHAR(2048)" remarks="适用范围，通用\限定范围">
            </column>
            <column name="SCOPE_OBJ" type="${clob.type}" remarks="适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员">
            </column>
            <column name="STATE" type="INT" remarks="状态，启用\停用">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-13" author="duqiang" failOnError="false">
        <createTable tableName="I_TEMPLATE_AUTH" remarks="模板权限">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TEMPLATE_ID" type="VARCHAR(64)">
            </column>
            <column name="SCOPE_OBJ" type="VARCHAR(64)" remarks="适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员">
            </column>
            <column name="SCOPE_ID" type="VARCHAR(64)">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-15" author="duqiang" failOnError="false">
        <createTable tableName="I_TEMPLATE_INDICATOR_RELATION" remarks="模板与指标关系表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TEMPLATE_ID" type="VARCHAR(64)" remarks="模板主键">
            </column>
            <column name="INDICATOR_ID" type="VARCHAR(64)" remarks="指标主键">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-16" author="wangkui" failOnError="false">
        <sql>
            create or replace view inspect_org as
            select distinct og.*
            from
                org_group og
                    left join org_relation orl on
                    og.ID_ = orl.GROUP_ID_
            where
                orl.TYPE_ = 'groupUser'
              and og.PARENT_ID_ != 0 and orl.user_id_ in (
                    select distinct
                        u.ID_ as user_id
                    from
                        org_role g
                            left join org_relation r on
                            g.ID_ = r.GROUP_ID_
                            left join org_user u on
                            r.USER_ID_ = u.ID_
                    where
                        r.TYPE_ = 'userRole'
                      and g.ALIAS_ in ('G_ROLE_INSPECT_LEADER', 'G_ROLE_INSPECT_USER'))
        </sql>
    </changeSet>
    
    <changeSet id="sql-17" author="duqiang" failOnError="false">
        <createTable tableName="i_task_inst" remarks="任务与实例关系表">
            <column name="ID" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TASK_ID" type="VARCHAR(64)" remarks="任务ID">
            </column>
            <column name="INST_ID" type="VARCHAR(64)" remarks="实例ID">
            </column>
            <column name="ORDER_NO" type="VARCHAR(64)" remarks="工单号">
            </column>
            <column name="STATUS" type="VARCHAR(64)" remarks="实例状态">
            </column>
            <column name="CREATE_USER" type="VARCHAR(256)" remarks="创建人">
            </column>
            <column name="CREATE_BY" type="VARCHAR(64)" remarks="创建人ID">
            </column>
            <column name="CREATE_TIME" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="UPDATE_USER" type="VARCHAR(256)" remarks="修改人">
            </column>
            <column name="UPDATE_BY" type="VARCHAR(64)" remarks="修改人ID">
            </column>
            <column name="UPDATE_TIME" type="TIMESTAMP" remarks="修改时间">
            </column>
        </createTable>
    </changeSet>

    <changeSet id="sql-18" author="wangkui" failOnError="false">
        <sql>
            create or replace view inspect_user as
            select v_user.*, orl.GROUP_ID_ as org_id, og.NAME_ as org_name from
                (select distinct
                     u.ID_ as user_id, u.FULLNAME_ as user_name, u.ACCOUNT_ as account, u.MOBILE_ as mobile, u.SN_ as sn
                 from
                     org_role g
                         left join org_relation r on
                         g.ID_ = r.GROUP_ID_
                         left join org_user u on
                         r.USER_ID_ = u.ID_
                 where
                     r.TYPE_ = 'userRole'
                   and g.ALIAS_ in ('G_ROLE_INSPECT_LEADER', 'G_ROLE_INSPECT_USER')) as v_user
                    left join org_relation orl on v_user.user_id = orl.USER_ID_
                    left join org_group og on og.ID_ = orl.GROUP_ID_
            where orl.TYPE_ = 'groupUser' and og.PARENT_ID_ != 0
        </sql>
    </changeSet>
    
</databaseChangeLog>