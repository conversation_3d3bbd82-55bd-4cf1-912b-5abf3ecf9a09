package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源指标数据DAO接口
 */
@MapperScan
public interface ResourceIndexDao extends BaseDao<String, ResourceIndex> {
    
    /**
     * 获取资源指标数据列表
     * @param index 查询参数
     * @return 指标数据列表
     */
    List<ResourceIndexDTO> list(ResourceIndexDTO index);

    /**
     * 批量插入指标数据
     * @param indexes 指标数据列表
     */
    void insertBatch(List<ResourceIndex> indexes);

    /**
     * 批量更新指标数据
     * @param indexes 指标数据列表
     */
    void updateBatch(List<ResourceIndex> indexes);

    /**
     * 根据规则ID和时间范围查询指标数据
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByRuleIdAndTimeRange(@Param("ruleId") String ruleId,
                                               @Param("startTime") Date startTime,
                                               @Param("endTime") Date endTime);

    /**
     * 计算超过CPU高阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cpuHighThreshold CPU高阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByCpuHighThreshold(@Param("cInstId") String cInstId,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("cpuHighThreshold") Integer cpuHighThreshold);

    /**
     * 计算超过CPU低阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cpuLowThreshold CPU低阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByCpuLowThreshold(@Param("cInstId") String cInstId,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("cpuLowThreshold") Integer cpuLowThreshold);

    /**
     * 计算超过内存高阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param memoryHighThreshold 内存高阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByMemoryHighThreshold(@Param("cInstId") String cInstId,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime,
                                                 @Param("memoryHighThreshold") Integer memoryHighThreshold);

    /**
     * 计算超过内存低阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param memoryLowThreshold 内存低阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByMemoryLowThreshold(@Param("cInstId") String cInstId,
                                                @Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime,
                                                @Param("memoryLowThreshold") Integer memoryLowThreshold);

    /**
     * 计算超过磁盘高阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param diskHighThreshold 磁盘高阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByDiskHighThreshold(@Param("cInstId") String cInstId,
                                               @Param("startTime") Date startTime,
                                               @Param("endTime") Date endTime,
                                               @Param("diskHighThreshold") Integer diskHighThreshold);

    /**
     * 计算超过磁盘低阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param diskLowThreshold 磁盘低阈值
     * @return 采集频率总和
     */
    Integer sumCollectionFreqByDiskLowThreshold(@Param("cInstId") String cInstId,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("diskLowThreshold") Integer diskLowThreshold);

    /**
     * 计算总的采集频率
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总采集频率
     */
    Integer sumTotalCollectionFreq(@Param("cInstId") String cInstId,
                                 @Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);

    /**
     * 一次性计算所有阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cpuHighThreshold CPU高阈值
     * @param cpuLowThreshold CPU低阈值
     * @param memoryHighThreshold 内存高阈值
     * @param memoryLowThreshold 内存低阈值
     * @param diskHighThreshold 磁盘高阈值
     * @param diskLowThreshold 磁盘低阈值
     * @return Map包含各种阈值的采集频率总和
     */
    Map<String, Integer> sumCollectionFreqByAllThresholds(@Param("cInstId") String cInstId,
                                                         @Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime,
                                                         @Param("cpuHighThreshold") Integer cpuHighThreshold,
                                                         @Param("cpuLowThreshold") Integer cpuLowThreshold,
                                                         @Param("memoryHighThreshold") Integer memoryHighThreshold,
                                                         @Param("memoryLowThreshold") Integer memoryLowThreshold,
                                                         @Param("diskHighThreshold") Integer diskHighThreshold,
                                                         @Param("diskLowThreshold") Integer diskLowThreshold);

    /**
     * 根据资源ID和时间范围查询指标数据
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByCInstIdAndTimeRange(@Param("cInstId") String cInstId, 
                                                @Param("startTime") Date startTime, 
                                                @Param("endTime") Date endTime);

    /**
     * 删除指定时间之前的数据
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteBeforeTime(@Param("beforeTime") Date beforeTime);

    /**
     * 获取最新的指标数据
     * @param cInstId 资源ID
     * @return 最新指标数据
     */
    ResourceIndex getLatestByCInstId(@Param("cInstId") String cInstId);
}
