package cn.gwssi.ecloudframework.module.efficiency.core.task;

import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceEfficiencyManager;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.RuleServerRelationManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;
import cn.gwssi.ecloudframework.module.efficiency.core.util.EfficiencyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 系统能效定时任务调度器
 * TODO: 需要配置定时任务来调用这些方法
 */
@Component
public class EfficiencyTaskScheduler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(EfficiencyTaskScheduler.class);
    
    @Resource
    private ResourceEfficiencyManager resourceEfficiencyManager;
    
    @Resource
    private ResourceIndexManager resourceIndexManager;
    
    @Resource
    private ResourceResultManager resourceResultManager;
    
    @Resource
    private RuleServerRelationManager ruleServerRelationManager;

    /**
     * 处理资源能效数据
     * TODO: 配置定时任务，建议每日凌晨执行
     */
    public void processEfficiencyData() {
        LOGGER.info("开始处理资源能效数据");
        
        try {
            // 获取所有启用的规则
            List<ResourceEfficiency> enabledRules = resourceEfficiencyManager.getEnabledRules();
            
            for (ResourceEfficiency rule : enabledRules) {
                processRuleEfficiencyData(rule);
            }
            
            LOGGER.info("资源能效数据处理完成");
        } catch (Exception e) {
            LOGGER.error("处理资源能效数据失败", e);
        }
    }

    /**
     * 处理单个规则的能效数据
     * @param rule 规则
     */
    private void processRuleEfficiencyData(ResourceEfficiency rule) {
        try {
            LOGGER.info("处理规则 {} 的能效数据", rule.getRuleName());
            
            // 获取昨天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            
            // 设置时间范围（昨天的有效业务时间段）
            Date startTime = EfficiencyUtils.parseBusinessTime(yesterday, rule.getStartTime());
            Date endTime = EfficiencyUtils.parseBusinessTime(yesterday, rule.getEndTime());
            
            // 获取规则关联的服务器
            List<RuleServerRelation> serverRelations = ruleServerRelationManager.getByRuleId(rule.getId());
            
            for (RuleServerRelation serverRelation : serverRelations) {
                processServerEfficiencyData(rule, serverRelation, startTime, endTime, yesterday);
            }
            
        } catch (Exception e) {
            LOGGER.error("处理规则 {} 的能效数据失败", rule.getRuleName(), e);
        }
    }

    /**
     * 处理单个服务器的能效数据
     * @param rule 规则
     * @param serverRelation 服务器关系
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param processDate 处理日期
     */
    private void processServerEfficiencyData(ResourceEfficiency rule, RuleServerRelation serverRelation, 
                                           Date startTime, Date endTime, Date processDate) {
        try {
            // 获取服务器在指定时间范围内的指标数据
            List<ResourceIndex> indexes = resourceIndexManager.getByCInstIdAndTimeRange(
                    serverRelation.getCInstId(), startTime, endTime);
            
            if (indexes.isEmpty()) {
                LOGGER.warn("服务器 {} 在时间范围 {} - {} 内没有指标数据", 
                           serverRelation.getCiName(), startTime, endTime);
                return;
            }
            
            // 计算能效结果
            ResourceResult result = calculateEfficiencyResult(rule, serverRelation, indexes, processDate);
            
            // 保存结果
            resourceResultManager.create(result);
            
            LOGGER.debug("服务器 {} 的能效数据处理完成", serverRelation.getCiName());
            
        } catch (Exception e) {
            LOGGER.error("处理服务器 {} 的能效数据失败", serverRelation.getCiName(), e);
        }
    }

    /**
     * 计算能效结果
     * @param rule 规则
     * @param serverRelation 服务器关系
     * @param indexes 指标数据列表
     * @param processDate 处理日期
     * @return 能效结果
     */
    private ResourceResult calculateEfficiencyResult(ResourceEfficiency rule, RuleServerRelation serverRelation, 
                                                   List<ResourceIndex> indexes, Date processDate) {
        ResourceResult result = new ResourceResult();
        result.setId(UUID.randomUUID().toString().replace("-", ""));
        result.setRelationId(serverRelation.getId());
        result.setRuleId(rule.getId());
        result.setServerGroup(serverRelation.getServerGroup());
        result.setCInstId(serverRelation.getCInstId());
        result.setCiName(serverRelation.getCiName());
        result.setIpAddress(serverRelation.getIpAddress());
        result.setProcessDate(processDate);
        
        // 计算各项指标的负荷状态
        int cpuHighCount = 0, cpuLowCount = 0;
        int memoryHighCount = 0, memoryLowCount = 0;
        int diskHighCount = 0, diskLowCount = 0;
        
        for (ResourceIndex index : indexes) {
            // CPU负荷判断
            if (index.getCpuRate() != null) {
                if (index.getCpuRate() >= rule.getCpuHighThreshold()) {
                    cpuHighCount++;
                } else if (index.getCpuRate() <= rule.getCpuLowThreshold()) {
                    cpuLowCount++;
                }
            }
            
            // 内存负荷判断
            if (index.getMemoryRate() != null) {
                if (index.getMemoryRate() >= rule.getMemoryHighThreshold()) {
                    memoryHighCount++;
                } else if (index.getMemoryRate() <= rule.getMemoryLowThreshold()) {
                    memoryLowCount++;
                }
            }
            
            // 磁盘负荷判断
            if (index.getDiskRate() != null) {
                if (index.getDiskRate() >= rule.getDiskHighThreshold()) {
                    diskHighCount++;
                } else if (index.getDiskRate() <= rule.getDiskLowThreshold()) {
                    diskLowCount++;
                }
            }
        }
        
        // 计算负荷占比
        int totalCount = indexes.size();
        double cpuHighRate = totalCount > 0 ? (double) cpuHighCount / totalCount * 100 : 0;
        double cpuLowRate = totalCount > 0 ? (double) cpuLowCount / totalCount * 100 : 0;
        double memoryHighRate = totalCount > 0 ? (double) memoryHighCount / totalCount * 100 : 0;
        double memoryLowRate = totalCount > 0 ? (double) memoryLowCount / totalCount * 100 : 0;
        double diskHighRate = totalCount > 0 ? (double) diskHighCount / totalCount * 100 : 0;
        double diskLowRate = totalCount > 0 ? (double) diskLowCount / totalCount * 100 : 0;
        
        // 根据规则阈值判断是否为高/低负荷
        result.setCpuHighThreshold(cpuHighRate >= rule.getHighRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        result.setCpuLowThreshold(cpuLowRate >= rule.getLowRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        result.setMemoryHighThreshold(memoryHighRate >= rule.getHighRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        result.setMemoryLowThreshold(memoryLowRate >= rule.getLowRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        result.setDiskHighThreshold(diskHighRate >= rule.getHighRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        result.setDiskLowThreshold(diskLowRate >= rule.getLowRate() ? 
                EfficiencyConstants.ThresholdResult.YES.getText() : EfficiencyConstants.ThresholdResult.NO.getText());
        
        // TODO: 这里可以根据历史数据计算累计天数
        result.setCpuDays(0);
        result.setMemoryDays(0);
        result.setDiskDays(0);
        
        return result;
    }

    /**
     * 清理历史数据
     * TODO: 配置定时任务，建议每周执行一次
     */
    public void cleanHistoryData() {
        LOGGER.info("开始清理历史数据");
        
        try {
            // 清理30天前的指标数据
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date thirtyDaysAgo = calendar.getTime();
            
            int indexCount = resourceIndexManager.cleanHistoryData(thirtyDaysAgo);
            LOGGER.info("清理了 {} 条指标数据", indexCount);
            
            // 清理90天前的结果数据
            calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -90);
            Date ninetyDaysAgo = calendar.getTime();
            
            int resultCount = resourceResultManager.cleanHistoryData(ninetyDaysAgo);
            LOGGER.info("清理了 {} 条结果数据", resultCount);
            
            LOGGER.info("历史数据清理完成");
        } catch (Exception e) {
            LOGGER.error("清理历史数据失败", e);
        }
    }

    /**
     * 检查规则状态
     * TODO: 配置定时任务，建议每小时执行一次
     */
    public void checkRuleStatus() {
        LOGGER.info("开始检查规则状态");
        
        try {
            List<ResourceEfficiency> enabledRules = resourceEfficiencyManager.getEnabledRules();
            
            for (ResourceEfficiency rule : enabledRules) {
                // 检查规则配置是否有效
                if (!resourceEfficiencyManager.validateRule(rule)) {
                    LOGGER.warn("规则 {} 配置无效", rule.getRuleName());
                    continue;
                }
                
                // 检查规则是否有关联的服务器
                List<RuleServerRelation> serverRelations = ruleServerRelationManager.getByRuleId(rule.getId());
                if (serverRelations.isEmpty()) {
                    LOGGER.warn("规则 {} 没有关联的服务器", rule.getRuleName());
                }
            }
            
            LOGGER.info("规则状态检查完成");
        } catch (Exception e) {
            LOGGER.error("检查规则状态失败", e);
        }
    }
}
