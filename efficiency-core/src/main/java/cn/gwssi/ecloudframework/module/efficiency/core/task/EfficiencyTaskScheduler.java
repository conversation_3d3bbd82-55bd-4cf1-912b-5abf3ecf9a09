package cn.gwssi.ecloudframework.module.efficiency.core.task;

import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceEfficiencyManager;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.RuleServerRelationDao;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;
import cn.gwssi.ecloudframework.module.efficiency.core.util.EfficiencyUtils;
import cn.gwssi.ecloudframework.module.efficiency.core.util.EfficiencyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 系统能效定时任务调度器
 * TODO: 需要配置定时任务来调用这些方法
 */
@Component
public class EfficiencyTaskScheduler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(EfficiencyTaskScheduler.class);
    
    @Resource
    private ResourceEfficiencyManager resourceEfficiencyManager;
    
    @Resource
    private ResourceIndexManager resourceIndexManager;
    
    @Resource
    private ResourceResultManager resourceResultManager;
    
    @Resource
    private RuleServerRelationDao ruleServerRelationDao;

    /**
     * 处理资源能效数据
     * TODO: 配置定时任务，建议每日凌晨执行
     */
    public void processEfficiencyData() {
        LOGGER.info("开始处理资源能效数据");
        
        try {
            // 获取所有启用的规则
            List<ResourceEfficiency> enabledRules = resourceEfficiencyManager.getEnabledRules();
            
            for (ResourceEfficiency rule : enabledRules) {
                processRuleEfficiencyData(rule);
            }
            
            LOGGER.info("资源能效数据处理完成");
        } catch (Exception e) {
            LOGGER.error("处理资源能效数据失败", e);
        }
    }

    /**
     * 处理单个规则的能效数据
     * @param rule 规则
     */
    private void processRuleEfficiencyData(ResourceEfficiency rule) {
        try {
            LOGGER.info("处理规则 {} 的能效数据", rule.getRuleName());
            
            // 获取昨天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            
            // 设置时间范围（昨天的有效业务时间段）
            Date startTime = EfficiencyUtils.parseBusinessTime(yesterday, rule.getStartTime());
            Date endTime = EfficiencyUtils.parseBusinessTime(yesterday, rule.getEndTime());
            
            // 获取规则关联的服务器
            List<RuleServerRelation> serverRelations = ruleServerRelationDao.getByRuleId(rule.getId());
            
            for (RuleServerRelation serverRelation : serverRelations) {
                processServerEfficiencyData(rule, serverRelation, startTime, endTime, yesterday);
            }
            
        } catch (Exception e) {
            LOGGER.error("处理规则 {} 的能效数据失败", rule.getRuleName(), e);
        }
    }

    /**
     * 处理单个服务器的能效数据
     * @param rule 规则
     * @param serverRelation 服务器关系
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param processDate 处理日期
     */
    private void processServerEfficiencyData(ResourceEfficiency rule, RuleServerRelation serverRelation,
                                           Date startTime, Date endTime, Date processDate) {
        try {
            // 计算工作时长
            long workMinutes = EfficiencyUtils.calculateMinutesBetween(startTime, endTime);

            if (workMinutes <= 0) {
                LOGGER.warn("服务器 {} 的工作时长无效: {} - {}",
                           serverRelation.getCiName(), startTime, endTime);
                return;
            }

            // 计算能效结果（使用SQL计算）
            ResourceResult result = calculateEfficiencyResult(rule, serverRelation, startTime, endTime, processDate);

            // 保存结果
            resourceResultManager.create(result);

            LOGGER.debug("服务器 {} 的能效数据处理完成，工作时长: {} 分钟", serverRelation.getCiName(), workMinutes);

        } catch (Exception e) {
            LOGGER.error("处理服务器 {} 的能效数据失败", serverRelation.getCiName(), e);
        }
    }

    /**
     * 计算能效结果
     * @param rule 规则
     * @param serverRelation 服务器关系
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param processDate 处理日期
     * @return 能效结果
     */
    private ResourceResult calculateEfficiencyResult(ResourceEfficiency rule, RuleServerRelation serverRelation,
                                                   Date startTime, Date endTime, Date processDate) {
        ResourceResult result = createResourceResult(serverRelation, rule.getId(), processDate);

        // 计算工作时长（分钟数）
        long totalWorkMinutes = EfficiencyUtils.calculateMinutesBetween(startTime, endTime);

        // 使用一个SQL查询计算所有阈值的采集频率总和
        Map<String, Integer> freqSums = resourceIndexManager.sumCollectionFreqByAllThresholds(
                serverRelation.getCInstId(), startTime, endTime,
                rule.getCpuHighThreshold(), rule.getCpuLowThreshold(),
                rule.getMemoryHighThreshold(), rule.getMemoryLowThreshold(),
                rule.getDiskHighThreshold(), rule.getDiskLowThreshold());

        // 获取各项指标的采集频率总和
        Integer cpuHighFreqSum = freqSums.getOrDefault("cpuHighFreqSum", 0);
        Integer cpuLowFreqSum = freqSums.getOrDefault("cpuLowFreqSum", 0);
        Integer memoryHighFreqSum = freqSums.getOrDefault("memoryHighFreqSum", 0);
        Integer memoryLowFreqSum = freqSums.getOrDefault("memoryLowFreqSum", 0);
        Integer diskHighFreqSum = freqSums.getOrDefault("diskHighFreqSum", 0);
        Integer diskLowFreqSum = freqSums.getOrDefault("diskLowFreqSum", 0);

        // 计算规则中"总时长*占比"的阈值
        double highThresholdValue = totalWorkMinutes * rule.getHighRate() / 100.0;
        double lowThresholdValue = totalWorkMinutes * rule.getLowRate() / 100.0;

        // 判断是否超过阈值：超过阈值的采集频率总和 >= 总时长*占比
        setThresholdResults(result, cpuHighFreqSum, cpuLowFreqSum, memoryHighFreqSum,
                           memoryLowFreqSum, diskHighFreqSum, diskLowFreqSum,
                           highThresholdValue, lowThresholdValue);

        LOGGER.debug("服务器 {} 能效计算结果: 工作时长={}分钟, CPU高负荷频率={}/{}, 内存高负荷频率={}/{}, 磁盘高负荷频率={}/{}",
                serverRelation.getCiName(), totalWorkMinutes, cpuHighFreqSum, highThresholdValue,
                memoryHighFreqSum, highThresholdValue, diskHighFreqSum, highThresholdValue);

        return result;
    }

    /**
     * 创建资源结果对象
     * @param serverRelation 服务器关系
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 资源结果对象
     */
    private ResourceResult createResourceResult(RuleServerRelation serverRelation, String ruleId, Date processDate) {
        ResourceResult result = new ResourceResult();
        result.setId(UUID.randomUUID().toString().replace("-", ""));
        result.setRelationId(serverRelation.getId());
        result.setRuleId(ruleId);
        result.setServerGroup(serverRelation.getServerGroup());
        result.setCInstId(serverRelation.getCInstId());
        result.setCiName(serverRelation.getCiName());
        result.setIpAddress(serverRelation.getIpAddress());
        result.setProcessDate(processDate);

        // 初始化累计天数为0，后续可以根据历史数据计算
        result.setCpuDays(0);
        result.setMemoryDays(0);
        result.setDiskDays(0);

        return result;
    }

    /**
     * 设置所有阈值判断结果
     * @param result 结果对象
     * @param cpuHighFreqSum CPU高负荷频率总和
     * @param cpuLowFreqSum CPU低负荷频率总和
     * @param memoryHighFreqSum 内存高负荷频率总和
     * @param memoryLowFreqSum 内存低负荷频率总和
     * @param diskHighFreqSum 磁盘高负荷频率总和
     * @param diskLowFreqSum 磁盘低负荷频率总和
     * @param highThresholdValue 高负荷阈值
     * @param lowThresholdValue 低负荷阈值
     */
    private void setThresholdResults(ResourceResult result, Integer cpuHighFreqSum, Integer cpuLowFreqSum,
                                   Integer memoryHighFreqSum, Integer memoryLowFreqSum,
                                   Integer diskHighFreqSum, Integer diskLowFreqSum,
                                   double highThresholdValue, double lowThresholdValue) {
        result.setCpuHighThreshold(getThresholdResult(cpuHighFreqSum, highThresholdValue));
        result.setCpuLowThreshold(getThresholdResult(cpuLowFreqSum, lowThresholdValue));
        result.setMemoryHighThreshold(getThresholdResult(memoryHighFreqSum, highThresholdValue));
        result.setMemoryLowThreshold(getThresholdResult(memoryLowFreqSum, lowThresholdValue));
        result.setDiskHighThreshold(getThresholdResult(diskHighFreqSum, highThresholdValue));
        result.setDiskLowThreshold(getThresholdResult(diskLowFreqSum, lowThresholdValue));
    }

    /**
     * 判断是否超过阈值的工具方法
     * @param actualValue 实际值
     * @param thresholdValue 阈值
     * @return 是否超过阈值的文本结果
     */
    private String getThresholdResult(Integer actualValue, double thresholdValue) {
        return actualValue >= thresholdValue ?
                EfficiencyConstants.ThresholdResult.YES.getText() :
                EfficiencyConstants.ThresholdResult.NO.getText();
    }

    /**
     * 清理历史数据
     * TODO: 配置定时任务，建议每周执行一次
     */
    public void cleanHistoryData() {
        LOGGER.info("开始清理历史数据");
        
        try {
            // 清理30天前的指标数据
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date thirtyDaysAgo = calendar.getTime();
            
            int indexCount = resourceIndexManager.cleanHistoryData(thirtyDaysAgo);
            LOGGER.info("清理了 {} 条指标数据", indexCount);
            
            // 清理90天前的结果数据
            calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -90);
            Date ninetyDaysAgo = calendar.getTime();
            
            int resultCount = resourceResultManager.cleanHistoryData(ninetyDaysAgo);
            LOGGER.info("清理了 {} 条结果数据", resultCount);
            
            LOGGER.info("历史数据清理完成");
        } catch (Exception e) {
            LOGGER.error("清理历史数据失败", e);
        }
    }

    /**
     * 检查规则状态
     * TODO: 配置定时任务，建议每小时执行一次
     */
    public void checkRuleStatus() {
        LOGGER.info("开始检查规则状态");
        
        try {
            List<ResourceEfficiency> enabledRules = resourceEfficiencyManager.getEnabledRules();
            
            for (ResourceEfficiency rule : enabledRules) {
                // 检查规则配置是否有效
                if (!resourceEfficiencyManager.validateRule(rule)) {
                    LOGGER.warn("规则 {} 配置无效", rule.getRuleName());
                    continue;
                }
                
                // 检查规则是否有关联的服务器
                List<RuleServerRelation> serverRelations = ruleServerRelationDao.getByRuleId(rule.getId());
                if (serverRelations.isEmpty()) {
                    LOGGER.warn("规则 {} 没有关联的服务器", rule.getRuleName());
                }
            }
            
            LOGGER.info("规则状态检查完成");
        } catch (Exception e) {
            LOGGER.error("检查规则状态失败", e);
        }
    }
}
