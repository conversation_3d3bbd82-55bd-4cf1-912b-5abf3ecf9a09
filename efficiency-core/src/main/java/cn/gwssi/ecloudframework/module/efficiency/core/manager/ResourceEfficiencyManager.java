package cn.gwssi.ecloudframework.module.efficiency.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;

import java.util.List;

/**
 * 资源效能规则管理接口
 */
public interface ResourceEfficiencyManager extends Manager<String, ResourceEfficiency> {
    
    /**
     * 获取资源效能规则列表
     * @param pageQuery 分页查询参数
     * @param rule 规则查询条件
     * @return 分页结果
     */
    PageResult<ResourceEfficiency> list(PageQuery pageQuery, ResourceEfficiencyDTO rule);

    /**
     * 保存或更新规则
     * @param ruleDTO 规则DTO
     * @return 保存的规则
     */
    ResourceEfficiency saveOrUpdate(ResourceEfficiencyDTO ruleDTO);

    /**
     * 根据ID查询规则
     * @param id 规则ID
     * @return 规则
     */
    ResourceEfficiency getById(String id);

    /**
     * 根据规则名称查询规则
     * @param ruleName 规则名称
     * @return 规则
     */
    ResourceEfficiency getByRuleName(String ruleName);

    /**
     * 批量保存规则
     * @param rules 规则列表
     */
    void saveBatch(List<ResourceEfficiency> rules);

    /**
     * 更新规则状态
     * @param ruleId 规则ID
     * @param status 状态（1-启用，0-禁用）
     * @return 更新后的规则
     */
    ResourceEfficiency updateRuleStatus(String ruleId, Integer status);

    /**
     * 删除规则及其关联数据（支持批量删除）
     * @param ruleIds 规则ID列表
     * @return 删除成功的数量
     */
    int deleteRuleAndRelatedData(List<String> ruleIds);

    /**
     * 获取启用的规则列表
     * @return 启用的规则列表
     */
    List<ResourceEfficiency> getEnabledRules();

    /**
     * 验证规则配置
     * @param rule 规则
     * @return 验证结果
     */
    boolean validateRule(ResourceEfficiency rule);

    /**
     * 复制规则
     * @param sourceRuleId 源规则ID
     * @param newRuleName 新规则名称
     * @return 复制的规则
     */
    ResourceEfficiency copyRule(String sourceRuleId, String newRuleName);
}
