package cn.gwssi.ecloudframework.module.efficiency.api.model;

import cn.gwssi.ecloudframework.base.db.model.BaseModel;
import lombok.Data;

/**
 * 规则服务器关系DTO
 */
@Data
public class RuleServerRelationDTO extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * 资源类别：物理机、容器
     */
    private String resourceType;

    /**
     * 规则名称（用于查询显示）
     */
    private String ruleName;

    /**
     * 是否已关联（用于前端显示）
     */
    private Boolean isAssociated;

    /**
     * 最近指标数据时间（用于前端显示）
     */
    private String lastDataTime;

    /**
     * 资源状态（用于前端显示）
     */
    private String resourceStatus;
}
