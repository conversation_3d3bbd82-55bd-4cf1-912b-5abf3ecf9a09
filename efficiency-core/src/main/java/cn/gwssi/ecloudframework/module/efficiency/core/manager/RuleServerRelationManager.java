package cn.gwssi.ecloudframework.module.efficiency.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;

import java.util.List;

/**
 * 规则服务器关系管理接口
 */
public interface RuleServerRelationManager extends Manager<String, RuleServerRelation> {
    
    /**
     * 获取规则服务器关系列表
     * @param pageQuery 分页查询参数
     * @param relation 关系查询条件
     * @return 分页结果
     */
    PageResult<RuleServerRelationDTO> list(PageQuery pageQuery, RuleServerRelationDTO relation);

    /**
     * 批量保存关系数据
     * @param relations 关系数据列表
     */
    void saveBatch(List<RuleServerRelation> relations);

    /**
     * 根据规则ID获取关联的服务器列表
     * @param ruleId 规则ID
     * @return 服务器关系列表
     */
    List<RuleServerRelation> getByRuleId(String ruleId);

    /**
     * 根据资源ID获取关联的规则列表
     * @param cInstId 资源ID
     * @return 规则关系列表
     */
    List<RuleServerRelation> getByCInstId(String cInstId);

    /**
     * 为规则批量关联服务器
     * @param ruleId 规则ID
     * @param serverIds 服务器ID列表
     * @return 关联成功的数量
     */
    int associateServersToRule(String ruleId, List<String> serverIds);

    /**
     * 为规则取消关联服务器
     * @param ruleId 规则ID
     * @param serverIds 服务器ID列表
     * @return 取消关联成功的数量
     */
    int disassociateServersFromRule(String ruleId, List<String> serverIds);

    /**
     * 根据规则ID删除所有关系
     * @param ruleId 规则ID
     * @return 删除的记录数
     */
    int deleteByRuleId(String ruleId);

    /**
     * 根据资源ID删除所有关系
     * @param cInstId 资源ID
     * @return 删除的记录数
     */
    int deleteByCInstId(String cInstId);

    /**
     * 检查规则和服务器的关系是否存在
     * @param ruleId 规则ID
     * @param cInstId 资源ID
     * @return 是否存在
     */
    boolean existsRelation(String ruleId, String cInstId);

    /**
     * 根据服务器群组获取服务器列表
     * @param serverGroup 服务器群组
     * @return 服务器关系列表
     */
    List<RuleServerRelation> getByServerGroup(String serverGroup);

    /**
     * 同步规则的服务器关系
     * @param ruleId 规则ID
     * @param serverGroups 服务器群组列表
     * @return 同步结果
     */
    boolean syncRuleServerRelations(String ruleId, List<String> serverGroups);

    /**
     * 获取未关联任何规则的服务器列表
     * @return 未关联服务器列表
     */
    List<RuleServerRelation> getUnassociatedServers();
}
