package cn.gwssi.ecloud;

import cn.gwssi.ecloudframework.base.api.query.PageQuery;
import cn.gwssi.ecloudframework.base.api.response.PageResult;
import cn.gwssi.ecloudframework.base.api.response.ResultMsg;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Date;
import java.util.Map;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class ResourceResultControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResourceResultManager resourceResultManager;

    @Test
    public void chain() throws Exception {
        this.testGetResultList();
        this.testGetEfficiencyReport();
        this.testGetLoadResources();
        this.testCleanHistoryData();
    }

    @Test
    public void testGetResultList() throws Exception {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNo(1);
        pageQuery.setPageSize(10);

        ResourceResultDTO queryDTO = new ResourceResultDTO();
        queryDTO.setServerGroup("测试服务器组ABC");
        queryDTO.setCiName("测试服务器ABC");

        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "10")
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = listResult.getResponse().getContentAsString();
        // 验证返回结果包含分页信息
        Object rows = JSONPath.read(responseContent, "$.rows");
        Object total = JSONPath.read(responseContent, "$.total");
    }

    @Test
    public void testGetEfficiencyReport() throws Exception {
        MvcResult reportResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/report")
                                .param("startDate", "2023-01-01")
                                .param("endDate", "2023-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = reportResult.getResponse().getContentAsString();
        // 验证返回结果包含报告数据
        Object data = JSONPath.read(responseContent, "$.data");
    }

    @Test
    public void testGetLoadResources() throws Exception {
        MvcResult loadResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/load-resources")
                                .param("startDate", "2023-01-01")
                                .param("endDate", "2023-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = loadResult.getResponse().getContentAsString();
        // 验证返回结果包含高负荷和低负荷资源列表
        Object data = JSONPath.read(responseContent, "$.data");
        Object highLoadResources = JSONPath.read(responseContent, "$.data.highLoadResources");
        Object lowLoadResources = JSONPath.read(responseContent, "$.data.lowLoadResources");
        Object highLoadCount = JSONPath.read(responseContent, "$.data.highLoadCount");
        Object lowLoadCount = JSONPath.read(responseContent, "$.data.lowLoadCount");
    }

    @Test
    public void testCleanHistoryData() throws Exception {
        MvcResult cleanResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/clean")
                                .param("beforeDate", "2023-01-01")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = cleanResult.getResponse().getContentAsString();
        // 验证返回结果包含清理信息
        Object data = JSONPath.read(responseContent, "$.data");
    }

    @Test
    public void testGetEfficiencyReportWithDifferentDateRange() throws Exception {
        // 测试不同的日期范围
        MvcResult reportResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/report")
                                .param("startDate", "2023-06-01")
                                .param("endDate", "2023-06-30")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = reportResult.getResponse().getContentAsString();
        Object success = JSONPath.read(responseContent, "$.success");
    }

    @Test
    public void testGetLoadResourcesWithShortDateRange() throws Exception {
        // 测试短时间范围的负荷资源查询
        MvcResult loadResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/load-resources")
                                .param("startDate", "2023-12-01")
                                .param("endDate", "2023-12-07")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = loadResult.getResponse().getContentAsString();
        Object success = JSONPath.read(responseContent, "$.success");
    }

    @Test
    public void testGetResultListWithComplexQuery() throws Exception {
        // 测试复杂查询条件
        ResourceResultDTO queryDTO = new ResourceResultDTO();
        queryDTO.setServerGroup("测试服务器组ABC");
        queryDTO.setCiName("测试服务器ABC");
        queryDTO.setApplicationName("测试应用ABC");
        queryDTO.setCpuHighThreshold("是");
        queryDTO.setMemoryHighThreshold("否");
        queryDTO.setDiskHighThreshold("是");

        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "20")
                ).andExpect(status().isOk())
                .andReturn();

        String responseContent = listResult.getResponse().getContentAsString();
        Object success = JSONPath.read(responseContent, "$.success");
    }
}
