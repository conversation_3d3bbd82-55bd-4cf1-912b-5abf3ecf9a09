package cn.gwssi.ecloud;

import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
public class ChainTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResourceIndexManager indexManager;

    @Test
    public void chain() throws Exception {
        ResourceIndexDTO resourceIndex = new ResourceIndexDTO();
        resourceIndex.setCInstId("123");

        MvcResult createResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/save")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(resourceIndex))
                ).andExpect(status().isOk())
//                .andExpect(JSONPath.read("$.id").exists())
                .andReturn();

    }


}
