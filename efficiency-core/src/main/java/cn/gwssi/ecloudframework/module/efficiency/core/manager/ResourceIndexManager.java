package cn.gwssi.ecloudframework.module.efficiency.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源指标数据管理接口
 */
public interface ResourceIndexManager extends Manager<String, ResourceIndex> {
    
    /**
     * 获取资源指标数据列表
     * @param pageQuery 分页查询参数
     * @param index 指标查询条件
     * @return 分页结果
     */
    PageResult<ResourceIndexDTO> list(PageQuery pageQuery, ResourceIndexDTO index);

    /**
     * 保存指标数据
     * @param index 指标数据
     * @return 保存的指标数据
     */
    ResourceIndex saveIndex(ResourceIndex index);

    /**
     * 批量保存指标数据
     * @param indexes 指标数据列表
     */
    void saveBatch(List<ResourceIndex> indexes);

    /**
     * 根据规则ID和时间范围查询指标数据
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByRuleIdAndTimeRange(String ruleId, Date startTime, Date endTime);

    /**
     * 根据资源ID和时间范围查询指标数据
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByCInstIdAndTimeRange(String cInstId, Date startTime, Date endTime);



    /**
     * 清理历史数据
     * @param beforeTime 时间点
     * @return 清理的记录数
     */
    int cleanHistoryData(Date beforeTime);

    /**
     * 获取资源使用率统计
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> getResourceUsageStatistics(String ruleId, Date startTime, Date endTime);

    /**
     * 根据规则ID和时间范围查询指标数据
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByRuleIdAndTimeRange(String ruleId, Date startTime, Date endTime);

    /**
     * 获取异常资源列表
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常资源列表
     */
    List<ResourceIndex> getAbnormalResources(String ruleId, Date startTime, Date endTime);



    /**
     * 一次性计算所有阈值的采集频率总和
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cpuHighThreshold CPU高阈值
     * @param cpuLowThreshold CPU低阈值
     * @param memoryHighThreshold 内存高阈值
     * @param memoryLowThreshold 内存低阈值
     * @param diskHighThreshold 磁盘高阈值
     * @param diskLowThreshold 磁盘低阈值
     * @return Map包含各种阈值的采集频率总和
     */
    Map<String, Integer> sumCollectionFreqByAllThresholds(String cInstId, Date startTime, Date endTime,
                                                         Integer cpuHighThreshold, Integer cpuLowThreshold,
                                                         Integer memoryHighThreshold, Integer memoryLowThreshold,
                                                         Integer diskHighThreshold, Integer diskLowThreshold);

    /**
     * 计算资源负荷状态
     * @param index 指标数据
     * @param cpuHighThreshold CPU高阈值
     * @param cpuLowThreshold CPU低阈值
     * @param memoryHighThreshold 内存高阈值
     * @param memoryLowThreshold 内存低阈值
     * @param diskHighThreshold 磁盘高阈值
     * @param diskLowThreshold 磁盘低阈值
     * @return 负荷状态
     */
    String calculateLoadStatus(ResourceIndex index, 
                              Integer cpuHighThreshold, Integer cpuLowThreshold,
                              Integer memoryHighThreshold, Integer memoryLowThreshold,
                              Integer diskHighThreshold, Integer diskLowThreshold);
}
