package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

/**
 * 资源能效结果DAO接口
 */
@MapperScan
public interface ResourceResultDao extends BaseDao<String, ResourceResult> {
    
    /**
     * 获取资源能效结果列表
     * @param result 查询参数
     * @return 结果列表
     */
    List<ResourceResultDTO> list(ResourceResultDTO result);

    /**
     * 批量插入结果数据
     * @param results 结果数据列表
     */
    void insertBatch(List<ResourceResult> results);

    /**
     * 根据规则ID和处理日期查询结果
     * @param ruleId 规则ID
     * @param processDate 处理日期
     * @return 结果列表
     */
    List<ResourceResult> getByRuleIdAndProcessDate(@Param("ruleId") String ruleId,
                                                  @Param("processDate") Date processDate);

    /**
     * 根据资源ID和时间范围查询结果
     * @param cInstId 资源ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果列表
     */
    List<ResourceResult> getByCInstIdAndDateRange(@Param("cInstId") String cInstId,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate);

    /**
     * 删除指定日期之前的数据
     * @param beforeDate 日期点
     * @return 删除的记录数
     */
    int deleteBeforeDate(@Param("beforeDate") Date beforeDate);

    /**
     * 获取高负荷资源列表
     * @param ruleId 规则ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 高负荷资源列表
     */
    List<ResourceResult> getHighLoadResources(@Param("ruleId") String ruleId,
                                             @Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate);

    /**
     * 获取低负荷资源列表
     * @param ruleId 规则ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 低负荷资源列表
     */
    List<ResourceResult> getLowLoadResources(@Param("ruleId") String ruleId,
                                            @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate);
}
