package cn.gwssi.ecloudframework.module.efficiency.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 资源能效结果实体类
 */
@Data
public class ResourceResult extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 关系表ID
     */
    private String relationId;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * 关联应用名称
     */
    private String applicationName;

    /**
     * 关联应用ID
     */
    private String applicationId;

    /**
     * CPU是否是高负荷
     */
    private String cpuHighThreshold;

    /**
     * CPU是否是低负荷
     */
    private String cpuLowThreshold;

    /**
     * 内存是否是高负荷
     */
    private String memoryHighThreshold;

    /**
     * 内存是否是低负荷
     */
    private String memoryLowThreshold;

    /**
     * 磁盘是否是高负荷
     */
    private String diskHighThreshold;

    /**
     * 磁盘是否是低负荷
     */
    private String diskLowThreshold;

    /**
     * CPU高负荷累计天数
     */
    private Integer cpuDays;

    /**
     * 内存高负荷累计天数
     */
    private Integer memoryDays;

    /**
     * 磁盘高负荷累计天数
     */
    private Integer diskDays;

    /**
     * 数据处理时间
     */
    private Date processDate;

    // Getter and Setter methods for all fields
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getRelationId() { return relationId; }
    public void setRelationId(String relationId) { this.relationId = relationId; }

    public String getRuleId() { return ruleId; }
    public void setRuleId(String ruleId) { this.ruleId = ruleId; }

    public String getServerGroup() { return serverGroup; }
    public void setServerGroup(String serverGroup) { this.serverGroup = serverGroup; }

    public String getCInstId() { return cInstId; }
    public void setCInstId(String cInstId) { this.cInstId = cInstId; }

    public String getCiName() { return ciName; }
    public void setCiName(String ciName) { this.ciName = ciName; }

    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }

    public String getApplicationName() { return applicationName; }
    public void setApplicationName(String applicationName) { this.applicationName = applicationName; }

    public String getApplicationId() { return applicationId; }
    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }

    public String getCpuHighThreshold() { return cpuHighThreshold; }
    public void setCpuHighThreshold(String cpuHighThreshold) { this.cpuHighThreshold = cpuHighThreshold; }

    public String getCpuLowThreshold() { return cpuLowThreshold; }
    public void setCpuLowThreshold(String cpuLowThreshold) { this.cpuLowThreshold = cpuLowThreshold; }

    public String getMemoryHighThreshold() { return memoryHighThreshold; }
    public void setMemoryHighThreshold(String memoryHighThreshold) { this.memoryHighThreshold = memoryHighThreshold; }

    public String getMemoryLowThreshold() { return memoryLowThreshold; }
    public void setMemoryLowThreshold(String memoryLowThreshold) { this.memoryLowThreshold = memoryLowThreshold; }

    public String getDiskHighThreshold() { return diskHighThreshold; }
    public void setDiskHighThreshold(String diskHighThreshold) { this.diskHighThreshold = diskHighThreshold; }

    public String getDiskLowThreshold() { return diskLowThreshold; }
    public void setDiskLowThreshold(String diskLowThreshold) { this.diskLowThreshold = diskLowThreshold; }

    public Integer getCpuDays() { return cpuDays; }
    public void setCpuDays(Integer cpuDays) { this.cpuDays = cpuDays; }

    public Integer getMemoryDays() { return memoryDays; }
    public void setMemoryDays(Integer memoryDays) { this.memoryDays = memoryDays; }

    public Integer getDiskDays() { return diskDays; }
    public void setDiskDays(Integer diskDays) { this.diskDays = diskDays; }

    public Date getProcessDate() { return processDate; }
    public void setProcessDate(Date processDate) { this.processDate = processDate; }
}
