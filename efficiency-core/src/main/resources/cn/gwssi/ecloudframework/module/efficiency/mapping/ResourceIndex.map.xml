<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceIndexDao">
    <resultMap id="ResourceIndex" type="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="cpuRate" column="cpu_rate" jdbcType="FLOAT"/>
        <result property="memoryRate" column="memory_rate" jdbcType="FLOAT"/>
        <result property="diskRate" column="disk_rate" jdbcType="FLOAT"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="sameDayStatus" column="same_day_status" jdbcType="VARCHAR"/>
        <result property="applicationName" column="application_name" jdbcType="VARCHAR"/>
        <result property="collectionFreq" column="collection_freq" jdbcType="INTEGER"/>
        <result property="ext1" column="ext1" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <resultMap id="ResourceIndexDTO" type="cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="cpuRate" column="cpu_rate" jdbcType="FLOAT"/>
        <result property="memoryRate" column="memory_rate" jdbcType="FLOAT"/>
        <result property="diskRate" column="disk_rate" jdbcType="FLOAT"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="sameDayStatus" column="same_day_status" jdbcType="VARCHAR"/>
        <result property="applicationName" column="application_name" jdbcType="VARCHAR"/>
        <result property="collectionFreq" column="collection_freq" jdbcType="INTEGER"/>
        <result property="ext1" column="ext1" jdbcType="LONGVARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_id, server_group, c_inst_id, ci_name, ip_address, cpu_rate, memory_rate, disk_rate,
        receive_time, send_time, same_day_status, application_name, collection_freq, ext1
    </sql>

    <select id="get" parameterType="java.lang.String" resultMap="ResourceIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_index
        WHERE id = #{id}
    </select>

    <select id="list" parameterType="cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO" 
            resultMap="ResourceIndexDTO">
        SELECT ri.<include refid="Base_Column_List"/>, re.rule_name
        FROM s_resource_index ri
        LEFT JOIN s_resource_efficiency re ON ri.rule_id = re.id
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND ri.rule_id = #{ruleId}
            </if>
            <if test="cInstId != null and cInstId != ''">
                AND ri.c_inst_id = #{cInstId}
            </if>
            <if test="ciName != null and ciName != ''">
                AND ri.ci_name LIKE CONCAT('%', #{ciName}, '%')
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                AND ri.ip_address LIKE CONCAT('%', #{ipAddress}, '%')
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                AND ri.server_group = #{serverGroup}
            </if>
            <if test="sameDayStatus != null and sameDayStatus != ''">
                AND ri.same_day_status = #{sameDayStatus}
            </if>
            <if test="startTime != null">
                AND ri.receive_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ri.receive_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ri.receive_time DESC
    </select>

    <select id="getByRuleIdAndTimeRange" resultMap="ResourceIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_index
        WHERE rule_id = #{ruleId}
        <if test="startTime != null">
            AND receive_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND receive_time &lt;= #{endTime}
        </if>
        ORDER BY receive_time DESC
    </select>

    <select id="getByCInstIdAndTimeRange" resultMap="ResourceIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_index
        WHERE c_inst_id = #{cInstId}
        <if test="startTime != null">
            AND receive_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND receive_time &lt;= #{endTime}
        </if>
        ORDER BY receive_time DESC
    </select>

    <select id="getLatestByCInstId" parameterType="java.lang.String" resultMap="ResourceIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_resource_index
        WHERE c_inst_id = #{cInstId}
        ORDER BY receive_time DESC
        LIMIT 1
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex">
        INSERT INTO s_resource_index (
            id, rule_id, server_group, c_inst_id, ci_name, ip_address, cpu_rate, memory_rate, disk_rate,
            receive_time, send_time, same_day_status, application_name, collection_freq, ext1
        ) VALUES (
            #{id}, #{ruleId}, #{serverGroup}, #{cInstId}, #{ciName}, #{ipAddress}, #{cpuRate}, #{memoryRate}, #{diskRate},
            #{receiveTime}, #{sendTime}, #{sameDayStatus}, #{applicationName}, #{collectionFreq}, #{ext1}
        )
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex">
        UPDATE s_resource_index SET
            rule_id = #{ruleId},
            server_group = #{serverGroup},
            c_inst_id = #{cInstId},
            ci_name = #{ciName},
            ip_address = #{ipAddress},
            cpu_rate = #{cpuRate},
            memory_rate = #{memoryRate},
            disk_rate = #{diskRate},
            receive_time = #{receiveTime},
            send_time = #{sendTime},
            same_day_status = #{sameDayStatus},
            application_name = #{applicationName},
            collection_freq = #{collectionFreq},
            ext1 = #{ext1}
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM s_resource_index WHERE id = #{id}
    </delete>

    <delete id="deleteBeforeTime">
        DELETE FROM s_resource_index WHERE receive_time &lt; #{beforeTime}
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO s_resource_index (
            id, rule_id, server_group, c_inst_id, ci_name, ip_address, cpu_rate, memory_rate, disk_rate,
            receive_time, send_time, same_day_status, application_name, collection_freq, ext1
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.ruleId}, #{item.serverGroup}, #{item.cInstId}, #{item.ciName}, #{item.ipAddress}, 
             #{item.cpuRate}, #{item.memoryRate}, #{item.diskRate}, #{item.receiveTime}, #{item.sendTime}, 
             #{item.sameDayStatus}, #{item.applicationName}, #{item.collectionFreq}, #{item.ext1})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE s_resource_index SET
                rule_id = #{item.ruleId},
                server_group = #{item.serverGroup},
                c_inst_id = #{item.cInstId},
                ci_name = #{item.ciName},
                ip_address = #{item.ipAddress},
                cpu_rate = #{item.cpuRate},
                memory_rate = #{item.memoryRate},
                disk_rate = #{item.diskRate},
                receive_time = #{item.receiveTime},
                send_time = #{item.sendTime},
                same_day_status = #{item.sameDayStatus},
                application_name = #{item.applicationName},
                collection_freq = #{item.collectionFreq},
                ext1 = #{item.ext1}
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>
