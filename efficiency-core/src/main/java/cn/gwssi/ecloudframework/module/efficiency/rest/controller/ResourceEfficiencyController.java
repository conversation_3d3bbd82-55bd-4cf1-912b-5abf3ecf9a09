package cn.gwssi.ecloudframework.module.efficiency.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceEfficiencyManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源效能规则控制器
 */
@RestController
@RequestMapping("/module/efficiency/rule")
public class ResourceEfficiencyController {
    
    @Resource
    private ResourceEfficiencyManager resourceEfficiencyManager;

    /**
     * 获取资源效能规则列表
     * @param pageQuery 分页查询参数
     * @param rule 规则查询条件
     * @return 规则列表
     */
    @PostMapping("/list")
    @ResponseBody
    public PageResult<ResourceEfficiency> list(PageQuery pageQuery, @RequestBody ResourceEfficiencyDTO rule) {
        return resourceEfficiencyManager.list(pageQuery, rule);
    }

    /**
     * 获取规则详情
     * @param id 规则ID
     * @return 规则详情
     */
    @GetMapping("/get/{id}")
    public ResultMsg<ResourceEfficiencyDTO> get(@PathVariable("id") String id) {
        ResourceEfficiency rule = resourceEfficiencyManager.get(id);
        ResourceEfficiencyDTO ruleDTO = BeanCopierUtils.transformBean(rule, ResourceEfficiencyDTO.class);
        return ResultMsg.SUCCESS(ruleDTO);
    }

    /**
     * 保存规则
     * @param ruleDTO 规则数据
     * @return 保存结果
     */
    @PostMapping("/save")
    @CatchErr
    public ResultMsg<String> save(@RequestBody ResourceEfficiencyDTO ruleDTO) {
        ResourceEfficiency savedRule = resourceEfficiencyManager.saveOrUpdate(ruleDTO);
        return ResultMsg.SUCCESS(savedRule.getId());
    }

    /**
     * 删除规则
     * @param id 规则ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    @CatchErr
    public ResultMsg<String> delete(@PathVariable("id") String id) {
        boolean success = resourceEfficiencyManager.deleteRuleAndRelatedData(id);
        if (success) {
            return ResultMsg.SUCCESS("删除成功");
        } else {
            return ResultMsg.FAIL("删除失败");
        }
    }

    /**
     * 启用规则
     * @param id 规则ID
     * @return 启用结果
     */
    @PostMapping("/enable/{id}")
    @CatchErr
    public ResultMsg<String> enable(@PathVariable("id") String id) {
        ResourceEfficiency rule = resourceEfficiencyManager.enableRule(id);
        return ResultMsg.SUCCESS("启用成功");
    }

    /**
     * 停用规则
     * @param id 规则ID
     * @return 停用结果
     */
    @PostMapping("/disable/{id}")
    @CatchErr
    public ResultMsg<String> disable(@PathVariable("id") String id) {
        ResourceEfficiency rule = resourceEfficiencyManager.disableRule(id);
        return ResultMsg.SUCCESS("停用成功");
    }

    /**
     * 复制规则
     * @param sourceId 源规则ID
     * @param newName 新规则名称
     * @return 复制结果
     */
    @PostMapping("/copy")
    @CatchErr
    public ResultMsg<String> copy(@RequestParam("sourceId") String sourceId, 
                                 @RequestParam("newName") String newName) {
        ResourceEfficiency newRule = resourceEfficiencyManager.copyRule(sourceId, newName);
        return ResultMsg.SUCCESS(newRule.getId());
    }

    /**
     * 获取启用的规则列表
     * @return 启用的规则列表
     */
    @GetMapping("/enabled")
    @ResponseBody
    public ResultMsg<List<ResourceEfficiency>> getEnabledRules() {
        List<ResourceEfficiency> rules = resourceEfficiencyManager.getEnabledRules();
        return ResultMsg.SUCCESS(rules);
    }

    /**
     * 验证规则名称是否重复
     * @param ruleName 规则名称
     * @param excludeId 排除的规则ID（用于编辑时验证）
     * @return 验证结果
     */
    @GetMapping("/validate/name")
    public ResultMsg<Boolean> validateRuleName(@RequestParam("ruleName") String ruleName,
                                              @RequestParam(value = "excludeId", required = false) String excludeId) {
        ResourceEfficiency existingRule = resourceEfficiencyManager.getByRuleName(ruleName);
        boolean isValid = existingRule == null || 
                         (excludeId != null && excludeId.equals(existingRule.getId()));
        return ResultMsg.SUCCESS(isValid);
    }

    /**
     * 批量删除规则
     * @param ids 规则ID列表
     * @return 删除结果
     */
    @PostMapping("/batch/delete")
    @CatchErr
    public ResultMsg<String> batchDelete(@RequestBody List<String> ids) {
        int successCount = 0;
        for (String id : ids) {
            if (resourceEfficiencyManager.deleteRuleAndRelatedData(id)) {
                successCount++;
            }
        }
        return ResultMsg.SUCCESS("成功删除 " + successCount + " 条记录");
    }
}
