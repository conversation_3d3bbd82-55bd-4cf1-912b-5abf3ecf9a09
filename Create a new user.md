# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 示例接口

> 创建人: 迈亚

> 更新人: 迈亚

> 创建时间: 2025-06-16 10:48:48

> 更新时间: 2025-06-16 10:48:48

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## Create a new user

> 创建人: 迈亚

> 更新人: 迈亚

> 创建时间: 2025-06-16 10:48:48

> 更新时间: 2025-06-16 10:48:48

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> https://rest.apipost.net/users

| 环境  | URL |
| --- | --- |


**Mock URL**

> /users?apipost_id=156e2bf363006

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"id": 0,
	"username": "username",
	"firstName": "",
	"lastName": "",
	"email": "",
	"password": "",
	"phone": "",
	"userStatus": 0
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | - | integer | 否 | - |
| username | - | string | 否 | - |
| firstName | - | string | 否 | - |
| lastName | - | string | 否 | - |
| email | - | string | 否 | - |
| password | - | string | 否 | - |
| phone | - | string | 否 | - |
| userStatus | - | integer | 否 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"error": 0,
	"msg": "",
	"user": {
		"id": 0,
		"username": "",
		"firstName": "",
		"lastName": "",
		"email": "",
		"password": "",
		"phone": "",
		"userStatus": 0
	}
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| error | - | integer | - |
| msg | - | string | - |
| user.id | - | integer | - |
| user.username | - | string | - |
| user.firstName | - | string | - |
| user.lastName | - | string | - |
| user.email | - | string | - |
| user.password | - | string | - |
| user.phone | - | string | - |
| user.userStatus | - | integer | - |
| user | - | object | - |

* 用户名已存在(200)

```javascript
{
	"error": 1,
	"msg": "Username already exists"
}
```

**Query**
