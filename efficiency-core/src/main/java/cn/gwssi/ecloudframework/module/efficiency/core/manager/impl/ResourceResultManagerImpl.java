package cn.gwssi.ecloudframework.module.efficiency.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceResultDao;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 资源能效结果管理实现类
 */
@Service
public class ResourceResultManagerImpl extends BaseManager<String, ResourceResult>
        implements ResourceResultManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceResultManagerImpl.class);
    
    @Resource
    private ResourceResultDao resourceResultDao;

    @Override
    public PageResult<ResourceResultDTO> list(PageQuery pageQuery, ResourceResultDTO result) {
        PageHelperUtils.startPageAndOrderBy(pageQuery, "process_date desc");
        List<ResourceResultDTO> list = resourceResultDao.list(result);
        
        // 为每个结果计算综合状态和建议
        for (ResourceResultDTO dto : list) {
            ResourceResult entity = new ResourceResult();
            entity.setCpuHighThreshold(dto.getCpuHighThreshold());
            entity.setCpuLowThreshold(dto.getCpuLowThreshold());
            entity.setMemoryHighThreshold(dto.getMemoryHighThreshold());
            entity.setMemoryLowThreshold(dto.getMemoryLowThreshold());
            entity.setDiskHighThreshold(dto.getDiskHighThreshold());
            entity.setDiskLowThreshold(dto.getDiskLowThreshold());
            
            dto.setOverallStatus(calculateOverallStatus(entity));
            dto.setRecommendation(generateRecommendation(entity));
        }
        
        return new PageResult<>(list);
    }

    @Override
    @Transactional
    public void saveBatch(List<ResourceResult> results) {
        if (results != null && !results.isEmpty()) {
            try {
                resourceResultDao.insertBatch(results);
            } catch (Exception e) {
                LOGGER.error("批量保存结果数据失败", e);
                throw new RuntimeException("批量保存结果数据失败：" + e.getMessage());
            }
        }
    }

    @Override
    public List<ResourceResult> getByRuleIdAndProcessDate(String ruleId, Date processDate) {
        return resourceResultDao.getByRuleIdAndProcessDate(ruleId, processDate);
    }

    @Override
    public List<ResourceResult> getByCInstIdAndDateRange(String cInstId, Date startDate, Date endDate) {
        return resourceResultDao.getByCInstIdAndDateRange(cInstId, startDate, endDate);
    }

    @Override
    @Transactional
    public int cleanHistoryData(Date beforeDate) {
        try {
            return resourceResultDao.deleteBeforeDate(beforeDate);
        } catch (Exception e) {
            LOGGER.error("清理历史数据失败", e);
            throw new RuntimeException("清理历史数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getEfficiencyReport(String ruleId, Date startDate, Date endDate) {
        List<ResourceResult> results = getByCInstIdAndDateRange(null, startDate, endDate);
        
        Map<String, Object> report = new HashMap<>();
        if (results.isEmpty()) {
            return report;
        }
        
        // 统计高负荷资源数量
        long highLoadCount = results.stream()
                .filter(r -> EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getCpuHighThreshold()) ||
                           EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getMemoryHighThreshold()) ||
                           EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getDiskHighThreshold()))
                .count();
        
        // 统计低负荷资源数量
        long lowLoadCount = results.stream()
                .filter(r -> EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getCpuLowThreshold()) &&
                           EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getMemoryLowThreshold()) &&
                           EfficiencyConstants.ThresholdResult.YES.getText().equals(r.getDiskLowThreshold()))
                .count();
        
        // 统计正常资源数量
        long normalCount = results.size() - highLoadCount - lowLoadCount;
        
        // 计算平均累计天数
        double avgCpuDays = results.stream().mapToInt(r -> r.getCpuDays() != null ? r.getCpuDays() : 0).average().orElse(0.0);
        double avgMemoryDays = results.stream().mapToInt(r -> r.getMemoryDays() != null ? r.getMemoryDays() : 0).average().orElse(0.0);
        double avgDiskDays = results.stream().mapToInt(r -> r.getDiskDays() != null ? r.getDiskDays() : 0).average().orElse(0.0);
        
        report.put("totalCount", results.size());
        report.put("highLoadCount", highLoadCount);
        report.put("lowLoadCount", lowLoadCount);
        report.put("normalCount", normalCount);
        report.put("highLoadRate", results.size() > 0 ? Math.round((double) highLoadCount / results.size() * 100.0) / 100.0 : 0.0);
        report.put("lowLoadRate", results.size() > 0 ? Math.round((double) lowLoadCount / results.size() * 100.0) / 100.0 : 0.0);
        report.put("normalRate", results.size() > 0 ? Math.round((double) normalCount / results.size() * 100.0) / 100.0 : 0.0);
        report.put("avgCpuDays", Math.round(avgCpuDays * 100.0) / 100.0);
        report.put("avgMemoryDays", Math.round(avgMemoryDays * 100.0) / 100.0);
        report.put("avgDiskDays", Math.round(avgDiskDays * 100.0) / 100.0);
        
        return report;
    }

    @Override
    public int getAbnormalResourceCount(String ruleId, Date processDate) {
        return resourceResultDao.getAbnormalResourceCount(ruleId, processDate);
    }

    @Override
    public List<ResourceResult> getHighLoadResources(String ruleId, Date processDate) {
        return resourceResultDao.getHighLoadResources(ruleId, processDate);
    }

    @Override
    public List<ResourceResult> getLowLoadResources(String ruleId, Date processDate) {
        return resourceResultDao.getLowLoadResources(ruleId, processDate);
    }

    @Override
    public String generateRecommendation(ResourceResult result) {
        if (result == null) {
            return "无建议";
        }
        
        List<String> recommendations = new ArrayList<>();
        
        // CPU建议
        if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getCpuHighThreshold())) {
            recommendations.add("CPU使用率过高，建议优化应用程序或增加CPU资源");
        } else if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getCpuLowThreshold())) {
            recommendations.add("CPU使用率过低，建议考虑资源回收或重新分配");
        }
        
        // 内存建议
        if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getMemoryHighThreshold())) {
            recommendations.add("内存使用率过高，建议优化内存使用或增加内存资源");
        } else if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getMemoryLowThreshold())) {
            recommendations.add("内存使用率过低，建议考虑资源回收或重新分配");
        }
        
        // 磁盘建议
        if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getDiskHighThreshold())) {
            recommendations.add("磁盘使用率过高，建议清理磁盘空间或增加存储资源");
        } else if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getDiskLowThreshold())) {
            recommendations.add("磁盘使用率过低，建议考虑资源回收或重新分配");
        }
        
        return recommendations.isEmpty() ? "资源使用正常" : String.join("；", recommendations);
    }

    @Override
    public String calculateOverallStatus(ResourceResult result) {
        if (result == null) {
            return EfficiencyConstants.LoadStatus.NORMAL.getText();
        }
        
        // 检查是否有高负荷
        if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getCpuHighThreshold()) ||
            EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getMemoryHighThreshold()) ||
            EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getDiskHighThreshold())) {
            return EfficiencyConstants.LoadStatus.HIGH.getText();
        }
        
        // 检查是否全部为低负荷
        if (EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getCpuLowThreshold()) &&
            EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getMemoryLowThreshold()) &&
            EfficiencyConstants.ThresholdResult.YES.getText().equals(result.getDiskLowThreshold())) {
            return EfficiencyConstants.LoadStatus.LOW.getText();
        }
        
        return EfficiencyConstants.LoadStatus.NORMAL.getText();
    }
}
