package cn.gwssi.ecloudframework.module.efficiency.core.model;

import cn.gwssi.ecloudframework.base.db.model.BaseModel;
import lombok.Data;

/**
 * 规则服务器关系实体类
 */
@Data
public class RuleServerRelation extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * 资源类别：物理机、容器
     */
    private String resourceType;
}
