package cn.gwssi.ecloudframework.module.inspection.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants;
import cn.gwssi.ecloudframework.module.inspection.api.model.FormItemDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.TaskInstDTO;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionFormItemDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionIndicatorDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskDao;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionTaskManager;
import cn.gwssi.ecloudframework.module.inspection.core.model.FormItem;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionIndicator;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionTask;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.service.UserService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡检任务管理实现类
 */
@Service
public class InspectionTaskManagerImpl extends BaseManager<String, InspectionTaskDTO> implements InspectionTaskManager {
    @Resource
    private InspectionTaskDao taskDao;

    @Resource
    private InspectionFormItemDao formItemDao;

    @Resource
    private InspectionIndicatorDao indicatorDao;

    @Resource
    private UserService userService;

    @Override
    public PageResult<InspectionTaskDTO> list(PageQuery pageQuery, InspectionTaskDTO task) {
        IUser currentUser = ContextUtil.getCurrentUser();
        List<? extends IUserRole> roles = currentUser.getRoles();
        boolean isLeader = roles.stream().anyMatch(item -> "G_ROLE_INSPECT_LEADER".equals(item.getAlias()));

        PageHelperUtils.startPageAndOrderBy(pageQuery, "task_time asc, state asc");
        List<InspectionTaskDTO> list = taskDao.list(task, isLeader, currentUser.getOrgId(), currentUser.getUserId());
        this.fillInst(list);
        return new PageResult<>(list);
    }

    @Override
    public PageResult<InspectionTaskDTO> listAll(PageQuery pageQuery, String planId) {
        PageHelperUtils.startPageAndOrderBy(pageQuery, "task_time desc");
        List<InspectionTaskDTO> list = taskDao.listAll(planId);
        this.fillInst(list);
        return new PageResult<>(list);
    }

    @Override
    public InspectionTaskDTO get(String taskId) {
        InspectionTaskDTO task = taskDao.getWithTemplate(taskId);
        List<FormItemDTO> formItems = formItemDao.list(taskId);
        List<TaskInstDTO> insts = taskDao.listInstByTaskId(Collections.singletonList(taskId));
        task.setFormItems(formItems);
        task.setTaskInsts(insts);

        return task;
    }

    @Override
    @Transactional
    public void setTaskInvalid(String taskId) {
        InspectionTask task = new InspectionTask();
        task.setEnabled(InspectionConstants.TaskValid.INVALID.getValue());
        taskDao.updateBatch(taskId.split(","), task);
    }

    @Override
    @Transactional
    public List<IUser> getInspectors() {
        String orgId = ContextUtil.getCurrentGroupId();
        return taskDao.getInspectors(orgId, "G_ROLE_INSPECT_USER");
    }

    @Override
    @Transactional
    public void assignInspector(String taskId, String receiver) {
        IUser user = userService.getUserById(receiver);

        InspectionTask task = new InspectionTask();
        task.setReceiver(receiver);
        task.setReceiverName(user.getFullname());
        task.setReceiveTime(new Date());
        task.setState(InspectionConstants.TaskState.PROCESSING.getText());

        taskDao.updateBatch(taskId.split(","), task);
    }

    @Override
    @Transactional
    public void receiveTask(String taskId, String receiver) {
        // 查询任务
        InspectionTaskDTO existTask = taskDao.get(taskId);
        if (null == existTask) {
            throw new BusinessException("任务不存在！");
        }

        IUser user = userService.getUserById(receiver);

        // 更新任务
        InspectionTaskDTO task = new InspectionTaskDTO();
        task.setId(taskId);
        task.setReceiver(receiver);
        task.setReceiverName(user.getFullname());
        task.setReceiveTime(new Date());
        task.setState(InspectionConstants.TaskState.PROCESSING.getText());
        taskDao.update(task);

        // 保存任务指标
        this.saveItems(taskId, existTask.getTemplateId(), existTask.getSubjectJson());
    }

    @Override
    @Transactional
    public void save(InspectionTaskDTO task) {
        // 更新任务
        InspectionTaskDTO updatedTask = new InspectionTaskDTO();
        updatedTask.setId(task.getId());
        updatedTask.setResult(task.getResult());
        updatedTask.setComment(task.getComment());
        updatedTask.setSubjectJson(task.getSubjectJson());
        updatedTask.setFiles(task.getFiles());

        // 提交时才更新状态
        if (InspectionConstants.TaskSubmit.SUBMIT.getValue().equals(task.getIsSubmit())) {
            updatedTask.setFinishTime(new Date());
            updatedTask.setState(InspectionConstants.TaskState.COMPLETED.getText());
        }
        taskDao.update(updatedTask);

        // 保存明细
        List<FormItemDTO> formItems = task.getFormItems();
        for (FormItemDTO formItem : formItems) {
            FormItemDTO updatedItem = new FormItemDTO();
            updatedItem.setId(formItem.getId());
            updatedItem.setResult(formItem.getResult());
            updatedItem.setComment(formItem.getComment());
            formItemDao.update(updatedItem);
        }
    }

    @Override
    @Transactional
    public void saveTask(InspectionTaskDTO taskDTO) {
        IUser user = ContextUtil.getCurrentUser();
        String taskId = IdUtil.getSuid();

        // 保存任务
        InspectionTaskDTO task = new InspectionTaskDTO();
        BeanUtils.copyProperties(taskDTO, task);
        task.setId(taskId);
        task.setReceiver(user.getUserId());
        task.setReceiverName(user.getFullname());
        task.setReceiveTime(new Date());
        task.setTType(InspectionConstants.TaskType.UNPLANNED.getText());
        task.setState(InspectionConstants.TaskState.PROCESSING.getText());
        task.setEnabled(InspectionConstants.TaskValid.VALID.getValue());
        taskDao.create(task);

        // 保存任务指标
        this.saveItems(taskId, task.getTemplateId(), task.getSubjectJson());
    }

    @Override
    public InspectionTaskDTO getErrorTask(String taskId, String subjectName) {
        InspectionTaskDTO task = taskDao.getWithTemplate(taskId);
        List<FormItemDTO> formItems = formItemDao.listWithError(taskId, subjectName);
        task.setFormItems(formItems);

        return task;
    }

    @Override
    public Map<String, Object> getStatistics(Map<String, String> params) {
        Map<String, Object> result = new HashMap<>();

        result.putAll(this.countByState(params));
        result.putAll(this.countByInst(params));

        List<Map<String, Object>> indicatorAndInsts = taskDao.countIndicatorAndInst(params);
        result.put("indicator_inst", indicatorAndInsts);
        result.put("indicator_inst_json", JSON.toJSONString(indicatorAndInsts));

        return result;
    }

    @Override
    public Map<String, Object> getTodayStatistics() {
        Map<String, Object> params = this.generateParams();

        Map<String, Object> result = new HashMap<>();
        result.putAll(this.countItemErr(params));
        result.putAll(this.countTaskState(params));
        result.putAll(this.countInstState(params));
        result.putAll(this.countItemRepeatedErr(params));
        return result;
    }

    @Override
    public List<Map<String, Object>> listToday(PageQuery pageQuery, String state) {
        Map<String, Object> params = this.generateParams();
        params.put("state", state);

        PageHelperUtils.startPageAndOrderBy(pageQuery, "endTime asc");
        return taskDao.listToday(params);
    }

    @Override
    public List<Map<String, Object>> listInst(PageQuery pageQuery, String date) {
        Map<String, Object> params = this.generateParams();
        params.put("date", date);

        PageHelperUtils.startPageAndOrderBy(pageQuery, "createTime desc");
        List<Map<String, Object>> list = taskDao.listInst(params);

        // 计算时长
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(m -> {
                LocalDateTime time = ((Timestamp)m.get("createTime")).toLocalDateTime();
                long between = ChronoUnit.MINUTES.between(time, LocalDateTime.now());
                String duration = (between / 60) + "小时" + (between % 60) + "分钟";
                m.put("duration", duration);
            });
        }

        return list;
    }

    @Override
    public Map<String, Object> getWorkOrder(String instId) {
        Map<String, Object> workOrder = taskDao.getWorkOrder(instId);
        List<Map<String, Object>> opinion = taskDao.getInstOpinion(instId);
        workOrder.put("opinion", opinion);
        return workOrder;
    }

    // 查询任务对应的实例
    private void fillInst(List<InspectionTaskDTO> list) {
        List<String> taskIds = list.stream().map(InspectionTaskDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(taskIds)) {

            List<TaskInstDTO> insts = taskDao.listInstByTaskId(taskIds);
            Map<String, List<TaskInstDTO>> instGroups = insts.stream().collect(Collectors.groupingBy(TaskInstDTO::getTaskId));
            list.forEach(task -> task.setTaskInsts(instGroups.get(task.getId())));
        }
    }

    // 保存任务指标
    private void saveItems(String taskId, String templateId, String subjectJson) {
        // 获取巡检对象
        List<String> subjects = null;
        if (StringUtils.isNotBlank(subjectJson)) {
            subjects = JSONPath.read(subjectJson, "$.subjectName", List.class);
        }
        if (CollectionUtils.isEmpty(subjects)) {
            throw new BusinessException("任务的巡检对象不存在！");
        }

        // 生成 form-item
        List<FormItem> items = new ArrayList<>();
        List<InspectionIndicator> indicators = indicatorDao.getIndicatorListByTemplateId(templateId);
        for (InspectionIndicator indicator : indicators) {
            for (String subject : subjects) {
                FormItem item = new FormItem();
                item.setTaskId(taskId);
                item.setIndicatorId(indicator.getId());
                item.setSubjectName(subject);
                items.add(item);
            }
        }
        formItemDao.insertBatch(items);
    }

    // 按任务状态统计
    private Map<String, Object> countByState(Map<String, String> params) {
        List<Map<String, Object>> list = taskDao.countStateAll(params);
        long completed = 0;
        long uncompleted = 0;
        for (Map<String, Object> map : list) {
            long count = (long)map.get("count");
            if (InspectionConstants.TaskState.COMPLETED.getText().equals(map.get("state"))) {
                completed = count;
            } else {
                uncompleted += count;
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("task_completed", completed);
        result.put("task_uncompleted", uncompleted);
        result.put("task_state", JSON.toJSONString(list));
        return result;
    }

    // 按实例状态、模板类型统计
    private Map<String, Object> countByInst(Map<String, String> params) {
        long completed = 0;
        long uncompleted = 0;
        List<Map<String, Object>> list = taskDao.countInstAll(params);
        for (Map<String, Object> map : list) {
            String status = (String)map.get("status");
            long count = (long)map.get("count");
            if ("end".equals(status)) {
                completed += count;
            } else {
                uncompleted += count;
            }
        }

        // 按 category 分组，累加每组的数量
        Map<String, Long> categorys = list.stream().collect(Collectors.groupingBy(m -> (String) m.get("category"),
                Collectors.summingLong(m -> (long) m.get("count"))));

        Map<String, Object> result = new HashMap<>();
        result.put("inst_completed", completed);
        result.put("inst_uncompleted", uncompleted);
        result.put("inst_template_category", categorys);
        result.put("inst_state", JSON.toJSONString(list));
        return result;
    }

    // 根据角色生成过滤条件
    private Map<String, Object> generateParams() {
        boolean isAdmin = false;
        boolean isLeader = false;
        IUser user = ContextUtil.getCurrentUser();
        List<? extends IUserRole> roles = user.getRoles();

        for (IUserRole role : roles) {
            if ("G_ROLE_INSPECT_MANAGER".equals(role.getAlias())) {
                isAdmin = true;
            }
            if ("G_ROLE_INSPECT_LEADER".equals(role.getAlias())) {
                isLeader = true;
            }
        }

        Map<String, Object> params = new HashMap<>();
        params.put("isAdmin", isAdmin);
        params.put("isLeader", isLeader);
        params.put("userId", user.getUserId());
        params.put("orgId", user.getOrgId());
        return params;
    }

    // 统计错误指标数量
    private Map<String, Object> countItemErr(Map<String, Object> params) {
        List<Map<String, Object>> itemErr = formItemDao.countErr(params);
        Map<String, Long> itemErrs = itemErr.stream().collect(Collectors.toMap(m -> m.get("date").toString(), m -> (long)m.get("count")));

        String today = DateUtil.today();
        String yesterday = DateUtil.formatDate(DateUtil.yesterday());

        // 获取今天、昨天的数量，计算增长
        long err_today = Optional.ofNullable(itemErrs.get(today)).orElse(0L);
        long err_yesterday = Optional.ofNullable(itemErrs.get(yesterday)).orElse(0L);
        double err_increase = 100;
        if (err_today <= 0) {
            err_increase = 0;
        } else if (err_yesterday > 0) {
            err_increase = BigDecimal.valueOf(100 * (err_today - err_yesterday)).divide(BigDecimal.valueOf(err_yesterday), 2, RoundingMode.HALF_UP).doubleValue();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("err_today", err_today);
        result.put("err_increase", err_increase + "%");
        result.put("err_item", JSON.toJSONString(itemErr));
        return result;
    }

    // 任务按状态统计数量
    private Map<String, Object> countTaskState(Map<String, Object> params) {
        List<Map<String, Object>> task = taskDao.countState(params);
        Map<String, Long> tasks = task.stream().collect(Collectors.toMap(m -> m.get("state").toString(), m -> (long)m.get("count")));
        long task_pending = Optional.ofNullable(tasks.get(InspectionConstants.TaskState.PENDING.getText())).orElse(0L);
        long task_processing = Optional.ofNullable(tasks.get(InspectionConstants.TaskState.PROCESSING.getText())).orElse(0L);
        long task_unfinished = task_pending + task_processing;

        Map<String, Object> result = new HashMap<>();
        result.put("task_unfinished", task_unfinished);
        result.put("task_pending", task_pending);
        result.put("task_processing", task_processing);
        result.put("task_state", JSON.toJSONString(tasks));
        return result;
    }

    // 按实例状态、级别统计
    private Map<String, Object> countInstState(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        List<Map<String, Object>> datas = taskDao.countInstState(params);
        long inst_processing = 0;
        long inst_completed = 0;
        for (Map<String, Object> data : datas) {
            String status = (String)data.get("status");
            String level = (String)data.get("level");
            long count = (long)data.get("count");

            if ("end".equals(status)) {
                inst_completed += count;
            } else {
                inst_processing += count;
                result.put("inst_processing_level_" + level, count);
            }
        }

        // 计算百分比
        double inst_completed_per = 0;
        double inst_total = inst_processing + inst_completed;
        if (inst_total > 0) {
            inst_completed_per = BigDecimal.valueOf(inst_completed * 100).divide(java.math.BigDecimal.valueOf(inst_total), 2, RoundingMode.HALF_UP).doubleValue();
        }

        result.put("inst_processing", inst_processing);
        result.put("inst_completed", inst_completed);
        result.put("inst_completed_per", inst_completed_per + "%");
        result.put("inst_state", JSON.toJSONString(datas));
        return result;
    }

    // 统计错误指标数量，重复次数
    private Map<String, Object> countItemRepeatedErr(Map<String, Object> params) {
        List<Map<String, Object>> errors = formItemDao.countErrRepeated(params);

        long sum = 0;
        if (CollectionUtils.isNotEmpty(errors)) {
            sum = errors.stream().mapToLong(m -> (long)m.get("count")).sum();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("inst_repeated", errors.size());
        result.put("inst_repeated_count", sum);
        result.put("item_repeated", JSON.toJSONString(errors));
        return result;
    }

}
