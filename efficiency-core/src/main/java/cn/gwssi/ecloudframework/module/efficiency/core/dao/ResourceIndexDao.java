package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

/**
 * 资源指标数据DAO接口
 */
@MapperScan
public interface ResourceIndexDao extends BaseDao<String, ResourceIndex> {
    
    /**
     * 获取资源指标数据列表
     * @param index 查询参数
     * @return 指标数据列表
     */
    List<ResourceIndexDTO> list(ResourceIndexDTO index);

    /**
     * 批量插入指标数据
     * @param indexes 指标数据列表
     */
    void insertBatch(List<ResourceIndex> indexes);

    /**
     * 批量更新指标数据
     * @param indexes 指标数据列表
     */
    void updateBatch(List<ResourceIndex> indexes);

    /**
     * 根据规则ID和时间范围查询指标数据
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByRuleIdAndTimeRange(@Param("ruleId") String ruleId, 
                                               @Param("startTime") Date startTime, 
                                               @Param("endTime") Date endTime);

    /**
     * 根据资源ID和时间范围查询指标数据
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    List<ResourceIndex> getByCInstIdAndTimeRange(@Param("cInstId") String cInstId, 
                                                @Param("startTime") Date startTime, 
                                                @Param("endTime") Date endTime);

    /**
     * 删除指定时间之前的数据
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteBeforeTime(@Param("beforeTime") Date beforeTime);

    /**
     * 获取最新的指标数据
     * @param cInstId 资源ID
     * @return 最新指标数据
     */
    ResourceIndex getLatestByCInstId(@Param("cInstId") String cInstId);
}
