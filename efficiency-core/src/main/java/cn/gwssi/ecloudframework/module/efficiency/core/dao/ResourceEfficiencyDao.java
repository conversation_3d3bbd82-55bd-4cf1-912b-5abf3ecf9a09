package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 资源效能规则DAO接口
 */
@MapperScan
public interface ResourceEfficiencyDao extends BaseDao<String, ResourceEfficiency> {
    
    /**
     * 获取资源效能规则列表
     * @param rule 查询参数
     * @return 规则列表
     */
    List<ResourceEfficiency> list(ResourceEfficiencyDTO rule);

    /**
     * 根据规则名称查询规则
     * @param ruleName 规则名称
     * @return 规则
     */
    ResourceEfficiency getByRuleName(String ruleName);

    /**
     * 批量插入规则
     * @param rules 规则列表
     */
    void insertBatch(List<ResourceEfficiency> rules);

    /**
     * 批量更新规则
     * @param rules 规则列表
     */
    void updateBatch(List<ResourceEfficiency> rules);

    /**
     * 获取启用的规则列表
     * @return 启用的规则列表
     */
    List<ResourceEfficiency> getEnabledRules();
}
