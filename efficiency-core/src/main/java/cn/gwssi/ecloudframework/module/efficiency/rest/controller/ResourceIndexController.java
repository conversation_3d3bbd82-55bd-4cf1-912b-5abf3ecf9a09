package cn.gwssi.ecloudframework.module.efficiency.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceIndex;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源指标数据控制器
 */
@RestController
@RequestMapping("/module/efficiency/index")
public class ResourceIndexController {
    
    @Resource
    private ResourceIndexManager resourceIndexManager;

    /**
     * 获取资源指标数据列表
     * @param pageQuery 分页查询参数
     * @param index 指标查询条件
     * @return 指标数据列表
     */
    @PostMapping("/list")
    public PageResult<ResourceIndexDTO> list(PageQuery pageQuery, @RequestBody ResourceIndexDTO index) {
        return resourceIndexManager.list(pageQuery, index);
    }

    /**
     * 获取指标数据详情
     * @param id 指标数据ID
     * @return 指标数据详情
     */
    @GetMapping("/get/{id}")
    public ResultMsg<ResourceIndexDTO> get(@PathVariable("id") String id) {
        ResourceIndex index = resourceIndexManager.get(id);
        ResourceIndexDTO indexDTO = BeanCopierUtils.transformBean(index, ResourceIndexDTO.class);
        return ResultMsg.SUCCESS(indexDTO);
    }

    /**
     * 保存指标数据
     * @param indexDTO 指标数据
     * @return 保存结果
     */
    @PostMapping("/save")
    @CatchErr
    public ResultMsg<String> save(@RequestBody ResourceIndexDTO indexDTO) {
        ResourceIndex index = BeanCopierUtils.transformBean(indexDTO, ResourceIndex.class);
        ResourceIndex savedIndex = resourceIndexManager.saveIndex(index);
        return ResultMsg.SUCCESS(savedIndex.getId());
    }

    /**
     * 批量保存指标数据
     * @param indexList 指标数据列表
     * @return 保存结果
     */
    @PostMapping("/batch/save")
    @CatchErr
    public ResultMsg<String> batchSave(@RequestBody List<ResourceIndexDTO> indexList) {
        List<ResourceIndex> indexes = BeanCopierUtils.transformList(indexList, ResourceIndex.class);
        resourceIndexManager.saveBatch(indexes);
        return ResultMsg.SUCCESS("批量保存成功，共 " + indexes.size() + " 条记录");
    }

    /**
     * 根据规则ID和时间范围查询指标数据
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    @GetMapping("/rule/{ruleId}")
    public ResultMsg<List<ResourceIndex>> getByRuleIdAndTimeRange(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        List<ResourceIndex> indexes = resourceIndexManager.getByRuleIdAndTimeRange(ruleId, startTime, endTime);
        return ResultMsg.SUCCESS(indexes);
    }

    /**
     * 根据资源ID和时间范围查询指标数据
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标数据列表
     */
    @GetMapping("/resource/{cInstId}")
    public ResultMsg<List<ResourceIndex>> getByCInstIdAndTimeRange(
            @PathVariable("cInstId") String cInstId,
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        List<ResourceIndex> indexes = resourceIndexManager.getByCInstIdAndTimeRange(cInstId, startTime, endTime);
        return ResultMsg.SUCCESS(indexes);
    }

    /**
     * 获取最新的指标数据
     * @param cInstId 资源ID
     * @return 最新指标数据
     */
    @GetMapping("/latest/{cInstId}")
    public ResultMsg<ResourceIndex> getLatest(@PathVariable("cInstId") String cInstId) {
        ResourceIndex index = resourceIndexManager.getLatestByCInstId(cInstId);
        return ResultMsg.SUCCESS(index);
    }

    /**
     * 获取资源使用率统计
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @GetMapping("/statistics/{ruleId}")
    public ResultMsg<Map<String, Object>> getStatistics(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        Map<String, Object> statistics = resourceIndexManager.getResourceUsageStatistics(ruleId, startTime, endTime);
        return ResultMsg.SUCCESS(statistics);
    }

    /**
     * 获取异常资源列表
     * @param ruleId 规则ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常资源列表
     */
    @GetMapping("/abnormal/{ruleId}")
    public ResultMsg<List<ResourceIndex>> getAbnormalResources(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        List<ResourceIndex> abnormalResources = resourceIndexManager.getAbnormalResources(ruleId, startTime, endTime);
        return ResultMsg.SUCCESS(abnormalResources);
    }

    /**
     * 清理历史数据
     * @param beforeTime 时间点
     * @return 清理结果
     */
    @PostMapping("/clean")
    @CatchErr
    public ResultMsg<String> cleanHistoryData(
            @RequestParam("beforeTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beforeTime) {
        int count = resourceIndexManager.cleanHistoryData(beforeTime);
        return ResultMsg.SUCCESS("清理完成，共删除 " + count + " 条记录");
    }

    /**
     * 删除指标数据
     * @param id 指标数据ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    @CatchErr
    public ResultMsg<String> delete(@PathVariable("id") String id) {
        resourceIndexManager.remove(id);
        return ResultMsg.SUCCESS("删除成功");
    }
}
