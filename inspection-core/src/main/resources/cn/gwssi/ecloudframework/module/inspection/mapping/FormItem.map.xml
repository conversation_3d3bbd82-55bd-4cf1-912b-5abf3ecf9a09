<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionFormItemDao">
    <resultMap id="FormItem" type="cn.gwssi.ecloudframework.module.inspection.api.model.FormItemDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="indicatorId" column="indicator_id" jdbcType="VARCHAR"/>
        <result property="displayItem" column="display_item" jdbcType="VARCHAR"/>
        <result property="subjectName" column="subject_name" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <association property="indicator" javaType="cn.gwssi.ecloudframework.module.inspection.api.model.InspectionIndicatorDTO">
            <result property="indicator" column="indicator" jdbcType="VARCHAR"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="standard" column="standard" jdbcType="VARCHAR"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="resultType" column="result_type" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="dicType" column="dic_type" jdbcType="VARCHAR"/>
        </association>
    </resultMap>
    
    <sql id="task_time_in">
        trunc(it.task_time) in (trunc(sysdate), trunc(sysdate) - 1)
    </sql>
    <sql id="task_time_in" databaseId="mysql">
        date(it.task_time) in (curdate(), curdate() - interval 1 DAY)
    </sql>
    <sql id="task_time_equal">
        trunc(it.task_time) = trunc(sysdate)
    </sql>
    <sql id="task_time_equal" databaseId="mysql">
        date(it.task_time) = curdate()
    </sql>
    <sql id="task_time_less">
        trunc(it.task_time) &lt; trunc(sysdate)
    </sql>
    <sql id="task_time_less" databaseId="mysql">
        date(it.task_time) &lt; curdate()
    </sql>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.api.model.FormItemDTO">
        UPDATE i_form_item
        <set>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="indicatorId != null and indicatorId != ''">
                indicator_id = #{indicatorId,jdbcType=VARCHAR},
            </if>
            <if test="displayItem != null and displayItem != ''">
                display_item = #{displayItem,jdbcType=VARCHAR},
            </if>
            <if test="subjectName != null and subjectName != ''">
                subject_name = #{subjectName,jdbcType=VARCHAR},
            </if>
            <if test="result != null and result != ''">
                result = #{result,jdbcType=VARCHAR},
            </if>
            <if test="comment != null and comment != ''">
                comment = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id}
    </update>
    
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO i_form_item
        (id, task_id, indicator_id, display_item, subject_name, result, comment,
        create_user, create_by, create_time, update_user, update_by, update_time)
        VALUES
        <foreach collection="indicators" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.taskId,jdbcType=VARCHAR}, #{item.indicatorId,jdbcType=VARCHAR}, #{item.displayItem,jdbcType=VARCHAR},
            #{item.subjectName,jdbcType=VARCHAR}, #{item.result,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUser,jdbcType=VARCHAR}, #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    
    <select id="list" resultMap="FormItem">
        SELECT f.*, i.indicator, i.method, i.standard, i.category, i.result_type, i.dic_type
        FROM i_form_item f
        LEFT JOIN i_indicator i on f.indicator_id  = i.id and i.state = '1'
        <where>
            <if test="taskId != null and taskId != ''">
                AND f.task_id = #{taskId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="listWithError" resultMap="FormItem">
        SELECT f.comment, i.`indicator`
        FROM i_form_item f
        LEFT JOIN i_indicator i on f.indicator_id  = i.id and i.state = '1'
        LEFT JOIN sys_data_dict d on d.DICT_KEY_ = 'UOMP_INSPECT_RESULT' and d.NAME_ like concat('%/', f.`result`)
        <where>
            <if test="taskId != null and taskId != ''">
                AND f.task_id = #{taskId}
            </if>
            <if test="subjectName != null and subjectName != ''">
                AND subject_name LIKE CONCAT('%', #{subjectName}, '%')
            </if>
            and d.ID_ is not null
        </where>
    </select>
    
    <!--  每日巡检使用的过滤条件，按角色拼接条件  -->
    <sql id="common_filter">
        <if test="!isAdmin">
            and (
                -- 巡检人是我
                it.RECEIVER = #{userId}
                -- 待接收，分配给我的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '1' and p.TASK_RECEIVER like concat('%', #{userId}, '%'))
                -- 待接收，分配给我的运维组织的
                or (it.STATE = '1' and p.TASK_RECEVIER_OBJ = '2' and p.TASK_RECEIVER like concat('%', #{orgId}, '%'))
                -- 待接收，我创建的计划
                <if test="isLeader">
                    or (it.STATE = '1' and p.CREATE_BY = #{userId})
                </if>
            )
        </if>
    </sql>
    
    <select id="countErr" resultType="java.util.HashMap">
        select date(it.task_time) as date, count(1) as count from i_form_item ifi
        LEFT JOIN i_task it on ifi.task_id = it.id
        LEFT JOIN i_plan p on it.PLAN_ID = p.id
        LEFT JOIN sys_data_dict d on d.DICT_KEY_ = 'UOMP_INSPECT_RESULT' and d.NAME_ like concat('%/', ifi.`result`)
        where it.enabled = '1' and <include refid="task_time_in" /> and d.ID_ is not null <include refid="common_filter" />
        group by date(it.task_time)
    </select>
    
    <select id="countErrRepeated" resultType="java.util.HashMap">
        select tmp1.indicator_id, tmp1.subject_name, count(1) as count from (
            select ifi.indicator_id, ifi.subject_name, it.task_time
            from i_form_item ifi
            left join i_task it on ifi.task_id = it.id
            LEFT JOIN i_plan p on it.PLAN_ID = p.id
            LEFT JOIN sys_data_dict d on d.DICT_KEY_ = 'UOMP_INSPECT_RESULT' and d.NAME_ like concat('%/', ifi.`result`)
            where it.enabled = '1' and d.ID_ is not null and <include refid="task_time_equal" /> <include refid="common_filter" />
        ) tmp1
        join (
            select ifi.indicator_id, ifi.subject_name, it.task_time
            from i_form_item ifi
            left join i_task it on ifi.task_id = it.id
            LEFT JOIN i_plan p on it.PLAN_ID = p.id
            LEFT JOIN sys_data_dict d on d.DICT_KEY_ = 'UOMP_INSPECT_RESULT' and d.NAME_ like concat('%/', ifi.`result`)
            where it.enabled = '1' and d.ID_ is not null and <include refid="task_time_less" /> <include refid="common_filter" />
        ) tmp2 on tmp1.indicator_id = tmp2.indicator_id and tmp1.subject_name = tmp2.subject_name
        group by tmp1.indicator_id, tmp1.subject_name
    </select>
    
</mapper>
