package cn.gwssi.ecloudframework.module.efficiency.api.model;

import cn.gwssi.ecloudframework.base.db.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 资源指标数据DTO
 */
@Data
public class ResourceIndexDTO extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * CPU使用率
     */
    private Float cpuRate;

    /**
     * 内存使用率
     */
    private Float memoryRate;

    /**
     * 磁盘使用率
     */
    private Float diskRate;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 当日状态
     */
    private String sameDayStatus;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 采集频率
     */
    private Integer collectionFreq;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 规则名称（用于查询显示）
     */
    private String ruleName;
}
